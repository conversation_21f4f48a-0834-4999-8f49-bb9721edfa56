<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Admin extends CI_Controller {

	/**

	 */
	 
	 function __construct()
    {
        parent::__construct();
		
		
		date_default_timezone_set('Asia/Dhaka');
		
		$this->load->library('session');
		$this->load->library('form_validation');
		$this->load->library('user_agent');
		$this->load->helper('security');
		$this->load->library("pagination");
		
		$curentdate=date('Ymd');
		$lincedate = '20281024';
		$advencdate = date('Ymd', mktime(0, 0, 0, date('m'), date('d') + 5, date('Y')));

	
		
		if ($this->session->userdata('admin_login') != "yes") {
			 $this->logout();
			//redirect('superadmin', 'refresh');
        	
        }
			$cactive = $this->db->get_where('company',array('id' =>1))->row()->active;
if($cactive==0){
		echo "<center><h1><font color='red'>Dear Customer Your Software Licence has been cancel. Please Pay your due bill and Contact Provider. No Request Accept ! </font></h1></center>";
		 exit;
	 }
	 if($cactive==3){
		echo "<center><h1><font color='red'>Dear Customer Your Software Licence has been cancel. Please <a href='https://forms.gle/sTraNwJ4qjgaRCN49' target='_blank'> Submit documents here </a> for active Licence! </font></h1></center>";
		 exit;
	 }     	
		
	}
	 
	  private function date_time(){
        //$timezone_offset = +6; // BD central time (gmt+6) for me
		//date_default_timezone_set("Asia/Dhaka");
        return $create_date = date('Y-m-d H:i:s');
     }
	 
	 
	public function index()
	{
		$data['page_title'] = 'Dashboard';

		$data['page_name'] = 'dashboard';
		$this->load->view('admin/index', $data);
	}
	
 	public function add_blocklist()
	{

	$create_date = $this->date_time();

	if($_POST){

	$var=$this->input->post();
	$service = $var['service'];
	$amount = $var['amount'];
	$code = $var['pcode'];



		$sql="INSERT INTO `block_list` (`id`, `service`, `amount`, `pcode`,`time`) VALUES (NULL, '$service', '$amount', '$code','$create_date')";
	$this->db->query($sql);
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add block_list By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
	redirect('admin/block_list/', 'refresh');	 	

	}else {

		$data['page_title'] = 'Add Commision ';
		$data['page_name'] = 'addblock';
	}

		
		$this->load->view('admin/index', $data);
	}

	
	
	
		public function block_list($id="") {


if(!empty($id)){
    
    
   	$this->db->where("id",$id);
    $this->db->delete("block_list");
	
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Comm Modual Delete Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		redirect('admin/block_list', 'refresh'); 
    
    
}

	$sql="select * from block_list order by id asc"; 
		 
	$query11 = $this->db->query($sql);		
	$reselerrate = $query11->result() ;
		
	$data["all_rate"] = $reselerrate;

	$data['page_title'] = 'Block list';
	$data['page_name'] = 'black_list';

	$this->load->view('admin/index', $data);

	} 
  
	public function backup()
	{
   
   if($_POST) {
    //ini_set('memory_limit', '-1');
	$sql="SELECT * from reseller where status=1 order by id asc";
	
	$query = $this->db->query($sql);
$num=$query->num_rows();
     $columnHeader = '';  
$columnHeader = "User Id" . "\t" . "Username" . "\t" . " Mobile" . "\t" . " Email" . "\t" . " Main Bal" . "\t" . " Bank Bal" . "\t" . " Drive Bal" . "\t". " Level" . "\t";  
$setData = '';  
      $rowData = ''; 
     
    
	foreach ($query->result() as $value){
        $value = '"' . $value->id . '"' . "\t".'"' . $value->username . '"' . "\t".'"' . $value->mobile . '"'. "\t".'"' . $value->email . '"' . "\t".'"' . $value->balance . '"' . "\t".'"' . $value->bank_balance . '"' . "\t".'"' . $value->drive_bal . '"' .  "\t".'"' . $value->custype . '"' . "\t"."\n"; 
      $rowData .= $value;  
   
   }
     $domain=$_SERVER['HTTP_HOST'];
$domain = str_replace(array('www.'), array(''), $domain);
$create_date=date('j-F-Y g i A').'_'.$domain;   
header("Content-type: application/octet-stream");  
header("Content-Disposition: attachment; filename=$create_date.xls");  
header("Pragma: no-cache");  
header("Expires: 0");  

     echo ucwords($columnHeader) . "\n" . $rowData . "\n";  
    
     exit;
   }
   
		$data['page_title'] = 'Backup';

		$data['page_name'] = 'backup';
		$this->load->view('admin/index', $data);
	}
	 
	public function response_provider($para1='', $para2='')
	{
		
			 
		foreach ($_POST as $key =>$value) 
				 { 
		$$key =  $value; 

		} 

		
		if($para1=='add') {
		$data['id']= $para2;
		$data['page_title'] = translate('response_from_provider');
		$data['page_name'] = 'response_from_add';
		
		}else if($para1=='update') {
		
		$ip = $_SERVER['REMOTE_ADDR']; 

		if(!empty($id)) {

		$queryup="UPDATE `response_from_provider` SET `product` = '$code', `prioriti` = '$prioriti', `must_be` = '$must_be', `before_number` = '$before_number', `after_number` = '$after_number', `before_amount` = '$before_amount', `after_amount` = '$after_amount', `trx_before` = '$trx_before', `trx_after` = '$trx_after', `before_sim` = '$before_sim', `after_sim` = '$after_sim', `trx_status` = '$response', `remark` = '$remark' WHERE `response_from_provider`.`id` = '$id'";
		
		
		$this->db->query($queryup);


		}else {

		$sql="INSERT INTO `response_from_provider` (`id`, `product`, `prioriti`, `must_be`, `before_number`, `after_number`, `before_amount`, `after_amount`, `trx_before`, `trx_after`, `before_sim`, `after_sim`, `trx_status`, `remark`, `refly_command`, `date`, `status`) VALUES (NULL, '$code', '$prioriti', '$must_be', '$before_number', '$after_number', '$before_amount', '$after_amount', '$trx_before', '$trx_after', '$before_sim', '$after_sim', '$response', '$remark', '$refly_command', '$date', '1')"; 
		
		$this->db->query($sql);
		}
		
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= ' Response  Add: '.$code;
		$this->mit->InsertActivetUser($uids,$msg);
		
		
		$this->session->set_flashdata('success', 'Added Success');

		redirect('admin/response_provider/', 'refresh');
			
			
		}else {
		
		//$varget=$this->input->get();
		$var=$this->input->post();
		
		$code=$var['code']; 
             
		$active=$var['active']; 
		
		$data['code']= $code;
		$data['active']= $active;
		
		$data['page_title'] = translate('response_from_provider');
		$data['page_name'] = 'response_from_provider';
		}
		$this->load->view('admin/index', $data);
	}
	
	public function sync_list($id,$service) {
	    
	    	$var=$this->input->post();
		
	
			
		if($_POST) {
	    

		$sql_api="SELECT * FROM `api_set` WHERE id='$var[route]' and status=1"; 

				$queryapifl = $this->db->query($sql_api);
				foreach ($queryapifl->result() as $row_api)
					{

				$api_id=$row_api->id; 
				$provider=$row_api->provider; 
				$flapi_userid=$row_api->userid; 
				$flapi_key=$row_api->api_key; 

				$api_url=$row_api->url; 
				$apirespons=$row_api->response; 
					}
					
				
			
if($provider==11){
    
 	$url_send ="http://".$api_url."/myportal/api/rechargeapi/recharge_api_thirdparty.php?access_id=$flapi_userid&access_pass=$flapi_key&service=BLCK";
				//$postdata = json_encode($data);
				
				
				$api_status=$this->mdb->sendPostData($url_send,$data);
				echo $api_status;
				
				$apists=json_decode($api_status);
				$responsests = $apists->STATUS;
				$balance = $apists->MAIN_BALANCE;   
    
}else{
    
    	$data = array(
				  "user" => $flapi_userid,
				  "key" => $flapi_key,
				   "op" => $id
				);
				
    if($service=='64'){
				$url_send ="http://".$api_url."/sendapi/drive_pack";
    }else{
        $url_send ="http://".$api_url."/sendapi/reguler_pack";
        
    }
				//$postdata = json_encode($data);
				
				  $header=array(
    'api-key: '.$flapi_key.'',
    'api-user: '.$flapi_userid.''
);
				$api_status=$this->mdb->sendPostData($url_send,$data,$header);
				//echo $api_status;
				
					$apists=json_decode($api_status, true);
				$responsests = $apists[status];
		$datap=	$apists[0][pack];
}
				if($responsests==1 or $responsests=='OK') 
                        { 
                    if($service=='64'){        
               	$this->db->where('op', $id);
		    	$this->db->delete('drive_package');             
                    }else{
                       $this->db->where('op', $id);
		    	$this->db->delete('net_package');   
                        
                    }          
					
	$sucdate=date('Y-m-d H:i:s'); 				
foreach($datap as $raws)
{
   
    
                   
    $addofer = array(
					'pk_name' =>$raws['title'],
					'op' =>$id,
					'price' =>$raws['price'],
					'comm' =>$raws['com'],
					'charge' =>0,
					'exp' =>$raws['exp'],
					'status' =>1,
					'volume'=>$raws['opname'],
					'date' =>$sucdate,
					'route' =>$var['route'],
					'owner_id' =>$raws['id'],
				);
if($service=='64'){
			$this->db->insert('drive_package',$addofer);
}else{
  	$this->db->insert('net_package',$addofer);  
    
}
    
    
}
					
						}
					if($service=='64'){	
					redirect('admin/big_pack/'.$id.'', 'refresh');
					}else{
					    redirect('admin/offer/'.$id.'', 'refresh');
					}
		}
	
	  	$data['page_title'] = 'synclist';
		$data['page_name'] = 'synclist';	  
		
			$this->load->view('admin/index', $data);
	    
	}
	
	
	public function manageuser($para1="", $para2="") {
		
		
	$user=$this->session->userdata('admin_session');
	$uid= $user->id;
	if($uid!=1) {
		redirect('admin', 'refresh');
	}
	$sendername = $user->username;
	$myLeft = $user->lft;
	$myRight = $user->rgt;

	$idate=date('Y-m-d');
	$create_date=date('j F Y g:i A'); 
	$ip = $_SERVER['REMOTE_ADDR']; 		
	$var=$this->input->post();
		
		if($para1=="add") {
			
		if($_POST) {
			
	
	//$varmenu=$this->input->post();
		
	$username  = $var['username'];
	$password  = $var['password'];
	$pin  	   = $var['upin'];
	$mobile    = $var['mobile'];
	$email     = $var['email'];
	$pincode     = $var['pincode'];
	$name     = $var['name'];
	
		
	$this->form_validation->set_rules('username', 'Username', 'is_unique[reseller.username]|trim|required|xss_clean|min_length[5]|alpha_dash');

	$this->form_validation->set_rules('password', 'Password', 'trim|required|xss_clean|min_length[6]|alpha_dash');
	$this->form_validation->set_rules('upin', 'PIN', 'trim|required|xss_clean|min_length[4]|alpha_dash');
	
	$this->form_validation->set_rules('pincode', 'Salf PIN', 'trim|required|xss_clean|min_length[4]');
	
	
		
	if ($this->form_validation->run() == FALSE) {
		$data['page_title'] = 'User Add';
		$data['page_name'] = 'manage_users/usersAdd';
	}else {
		
		$typ = 'admin';

		$otpchking = $this->mit->otpchk($pincode,$typ);

		if($otpchking) {
	
	$userpl=strlen($username); 
		$pass=strlen($password);

		$q2="UPDATE reseller SET rgt = rgt + 2 WHERE rgt > $myLeft"; 
		$this->db->query($q2);	

		$q3="UPDATE reseller SET lft = lft + 2 WHERE lft > $myLeft"; 
		$this->db->query($q3);


		$left=$myLeft +1; 
		$right=$myLeft + 2; 
		$hash = $this->mdb->generate($password); 
		$hashpin = $this->mdb->generate($pin);
		
		$useradd = array(
					'username' =>$username,
					'password' =>$hash,
					'pincode' =>$hashpin,
					'name' =>$name,
					'email' =>$email,
					'lft' =>$left,
					'rgt' =>$right,
					'type' =>0,
					'p_id' =>$uid,
					'mobile' =>$mobile,
					'custype' =>'admin',
					'tarif' =>0,
					'webAccess' =>1,
					'appsAccess' =>0,
					'enbale_otp' =>0,
					'status' =>1,
					'create_date' =>$create_date
					);

		$this->mdb->insert('reseller',$useradd);
		
		$last = $this->db->insert_id();
		
		$menuinsert = array(
					'userid' =>$last,
					'request_view' => $var['request_view'],
					'request_cancel' => $var['request_cancel'],
					'request_confirm' => $var['request_confirm'],
					'request_resend' => $var['request_resend'],
					'request_details' => $var['request_details'],
					'sms_view' => $var['sms_view'],
					'sms_delete' => $var['sms_delete'],
					'prepaid_history' => $var['prepaid_history'],
					'prepaid_add' => $var['prepaid_add'],
					'prepaid_manage' => $var['prepaid_manage'],
					'billpay_history' => $var['billpay_history'],
					'billpay_settings' => $var['billpay_settings'],
					'bank_history' => $var['bank_history'],
					'bank_settings' => $var['bank_settings'],
					'message_history' => $var['message_history'],
					'message_outbox' => $var['message_outbox'],
					'message_settings' => $var['message_settings'],
					'reseller_manage' => $var['reseller_manage'],
					'reseller_add' => $var['reseller_add'],
					'reseller_edit' => $var['reseller_edit'],
					'payment_history' => $var['payment_history'],
					'payment_add_return' => $var['payment_add_return'],
					'server_module' => $var['server_module'],
					'server_rate' => $var['server_rate'],
					'server_api' => $var['server_api'],
					'route' => $var['route'],
					'server_security' => $var['server_security'],
					'server_delete_res' => $var['server_delete_res'],
					'tarif_per' => $var['tarif_per'],
					'country_per' => $var['country_per'],
					'Oparetor_per' => $var['Oparetor_per'],
					'tools_branding' => $var['tools_branding'],
					'tools_latest_update' => $var['tools_latest_update'],
					'tools_notice' => $var['tools_notice'],
					'device_logs' => $var['device_logs'],
					'ip_restec' => $var['ip_restec'],
					'pay_log' => $var['pay_log'],
					'getway_set' => $var['getway_set'],
					'internet_per' => $var['internet_per'],
					'reports' => $var['reports'],
					'complain' => $var['complain'],
					'online_user' => $var['online_user'],
					'access_logs' => $var['access_logs'],
					'api_info' => $var['api_info'],
					'profile' => $var['profile']
					);
		
		$this->db->insert('menu_list',$menuinsert);
		
		
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= ' Manage User Add: '.$username;
		$this->mit->InsertActivetUser($uids,$msg);
		
		
		$this->session->set_flashdata('success', 'Added Success');

		redirect('admin/manageuser/', 'refresh');
		
		}else {
		
		$this->session->set_flashdata('error', 'PIN code Wrong');

		redirect('admin/manageuser/', 'refresh');
		
		}
		
		}
	
				
		}else {
		
		$data['page_title'] = 'User Add';
		$data['page_name'] = 'manage_users/usersAdd';	
			}

		}else if($para1=="edit") {
			
			if($para2==1) {
				redirect('admin/manageuser/', 'refresh');
			}
		
		$data['edit_user']=$this->mdb->getData('reseller',array('id' =>$para2));
		$data['menu_edit']=$this->mdb->getData('menu_list',array('userid' =>$para2));
		
		$data['page_title'] = 'User Edit';
		$data['page_name'] = 'manage_users/manage_edit';	
			
			
		}else if($para1=="update"){
			
				$name  = $var['name'];
				$mobile    = $var['mobile'];
				$email     = $var['email'];
				$active     = $var['active'];
				$pincode    = $var['pincode'];
				
		$typ = 'admin';

		$otpchking = $this->mit->otpchk($pincode,$typ);

		if($otpchking) {
	
			$userupdate = array(
					'name' =>$name,
					'mobile' =>$mobile,
					'email' =>$email,
					'status' =>$active
					);
					
		$this->db->where('id',$para2);
		$this->db->update('reseller',$userupdate);
		
		$reselleri = $this->db->get_where('reseller',array('id' =>$para2))->row()->username;
			
			$menuupdate = array(
					
					'request_view' => $var['request_view'],
					'request_cancel' => $var['request_cancel'],
					'request_confirm' => $var['request_confirm'],
					'request_resend' => $var['request_resend'],
					'request_details' => $var['request_details'],
					'sms_view' => $var['sms_view'],
					'sms_delete' => $var['sms_delete'],
					'prepaid_history' => $var['prepaid_history'],
					'prepaid_add' => $var['prepaid_add'],
					'prepaid_manage' => $var['prepaid_manage'],
					'billpay_history' => $var['billpay_history'],
					'billpay_settings' => $var['billpay_settings'],
					'bank_history' => $var['bank_history'],
					'bank_settings' => $var['bank_settings'],
					'message_history' => $var['message_history'],
					'message_outbox' => $var['message_outbox'],
					'message_settings' => $var['message_settings'],
					'reseller_manage' => $var['reseller_manage'],
					'reseller_add' => $var['reseller_add'],
					'reseller_edit' => $var['reseller_edit'],
					'payment_history' => $var['payment_history'],
					'payment_add_return' => $var['payment_add_return'],
					'server_module' => $var['server_module'],
					'server_rate' => $var['server_rate'],
					'server_api' => $var['server_api'],
					'route' => $var['route'],
					'server_security' => $var['server_security'],
					'server_delete_res' => $var['server_delete_res'],
					'tarif_per' => $var['tarif_per'],
					'country_per' => $var['country_per'],
					'Oparetor_per' => $var['Oparetor_per'],
					'tools_branding' => $var['tools_branding'],
					'tools_latest_update' => $var['tools_latest_update'],
					'tools_notice' => $var['tools_notice'],
					'device_logs' => $var['device_logs'],
					'ip_restec' => $var['ip_restec'],
					'pay_log' => $var['pay_log'],
					'getway_set' => $var['getway_set'],
					'internet_per' => $var['internet_per'],
					'reports' => $var['reports'],
					'complain' => $var['complain'],
					'online_user' => $var['online_user'],
					'access_logs' => $var['access_logs'],
					'api_info' => $var['api_info'],
					'profile' => $var['profile']
					);
					
		$this->db->where('userid',$para2);
		$this->db->update('menu_list',$menuupdate);
		
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= ' Manage User Edit: '.$reselleri;
		$this->mit->InsertActivetUser($uids,$msg);
		
		$this->session->set_flashdata('success', 'Update Success');

		redirect('admin/manageuser/', 'refresh');
		}else {
			
		$this->session->set_flashdata('error', 'PIN Code Wrong');

		redirect('admin/manageuser/', 'refresh');
			
		}
			
		}else {
			
	
		$data['page_title'] = 'Manage User';
		$data['page_name'] = 'manage_users/manage_user_list';	
		}
		
		$this->load->view('admin/index', $data);
	}

		public function RechargeStatus($para1="", $para2="") {

		$decideid = $this->mdb->passwordChanger('decrypt', $para2); 
	
		$sucdate=date('Y-m-d H:i:s'); 
		$create_date=date('Y-m-d H:i:s');
		$idate = date('Y-m-d');
		
		$var=$this->input->post();	
		$varget=$this->input->get();

		$trid= $var['trid']	;

	$req_type = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->remark;


	if($para1=="cancel"){

	if($_POST){

	$canstatus = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->status;

	if($canstatus!=3){
$amount = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->balance;
$service = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->service;
	$source = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->result;

	
	$sql="UPDATE `sendflexi` SET `actual_send_time`='$sucdate', `idate`='$idate', `trxid` = '$trid', `status` = '3', `local` = '3', `refund` = '3', `api` = '1' WHERE `sendflexi`.`id` ='$decideid' ";

	$this->db->query($sql); 

	$cansq="SELECT * FROM `trans` WHERE send_sl='$decideid'"; 
	$query = $this->db->query($cansq);
	foreach ($query->result() as $row_cancel){
 	$debitamt=$row_cancel->debit; 
  	$canuserid=$row_cancel->userid; 
  	$prebalance=$this->mit->accountBalance($canuserid,$source); 
	$accountbalance=$prebalance+$debitamt; 
  	$cantypr="plus"; 
 
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
		$resName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->mame;
	$parent_id = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;

	$mobile = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->phone;
	$amount = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->balance;
	$sid333 = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->sid;
	

	$this->mit->balanceUpdate($canuserid,$debitamt,$cantypr,$source); 
      
    $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `send_sl`, `p_id`, `date`, `time`) VALUES (NULL, '$canuserid', '$title_op Cancell By Admin Num: $mobile and amount : $amount Res: $resellercustomerName', '$prebalance', '', '$debitamt', '$accountbalance', '$cantypr', '$service', '$sid333', '3', '$decideid', '$parent_id', '$idate', '$sucdate');";

     $this->db->query($sql_tr); 

 	}
 	
 	$robi=substr($resellercustomerName , 0, 2);
 			
    $reamin=($prebalance+$debitamt);
    
//$msg ="$title_op Tk $amount to $mobile Faild for $trid Reaming BDT $reamin";
$msg="Your $title_op request of Tk $amount for $mobile was Faild. Your Update Balance $reamin Tk.";
$fb = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->facebook; 	

	
   $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){
	$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($resellercustomerName,$msg); 
		     }else{	
			    $this->mdb->sms_send($resellercustomerName,$msg);
		     }
  

}

 if($req_type=='apps'){
   $ftoken = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->note;
   if(!empty($ftoken)){
       
      $title ="$title_op cancel $mobile"; 
  $this->mit->send_fcm_msg($title,$msg,$ftoken,$decideid); 
   }
  
     
 }

if($req_type=='fb'){
$this->mdb->sendfb("flexiload","SMS*$fb*$msg*DD"); 
}


 	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Cancell '.$mobile.' and '.$amount. ' By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

	print("<script>window.alert('Manual Cancell');</script>");
	print("<script>
   	window.opener.location.reload();
    window.close();
	</script>");
	}else {
	print("<script>window.alert('Allready Cancell');</script>");
	print("<script>
   	window.opener.location.reload();
    window.close();
	</script>");
	}

		} // post end

		$data['id'] = $decideid;

		$this->load->view('admin/status/cancel', $data);

	}else if ($para1=="success") {

		if($_POST) {
		    
		$canuserid = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->userid;
	    	$com = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->commision;
	    		$number = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->phone;
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
		

	$sql="UPDATE `sendflexi` SET `actual_send_time`='$sucdate', `idate`='$idate', `trxid` = '$trid', `status` = '1', `local` = '1', `refund` = '1', `api` = '1' WHERE `sendflexi`.`id` ='$decideid' ";

		$ifevff=$this->db->query($sql);
  if($ife){	if($com!=0){
      	$direct_parent = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;
      
      	$prebalance=$this->mit->accountBalance($canuserid); 

      
      $t="plus"; 
		$balchange = $this->mit->balanceUpdate($canuserid,$com,$t);
			$bal=$this->mit->accountBalance($canuserid); 
      
		 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `p_id`, `date`, `time`) VALUES (NULL, '$canuserid', 'Commision for  $number', '$prebalance', '0', '$com', '$bal', 'plus', 'transfer', '$decideid', '$direct_parent', '$idate', '$create_date')"; 

		 $this->db->query($sql_tr);
$type="Transfer";
		$sql="INSERT INTO `pay_receive` (`id`, `trxid`, `userid`, `desc`, `remark`, `sender`, `p_id`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$decideid', '$canuserid', 'Commision By admin $description', 'By Admin', 'Admin', '$direct_parent', '$com', '$bal', '$type', 'recharge', '$idate', '$create_date')";

		$this->db->query($sql);
      
      
      $owne = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
      
      	$robi=substr($owne , 0, 2);

    
$msg ="You have Received Commision Tk $com for $trid New BDT $bal";
    
    
   $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){
	$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($owne,$msg); 
		     }else{	
			    $this->mdb->sms_send($owne,$msg);
		     }
  

}    
      
      
      
      
  }
  }
		
			$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Manual Success By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

    
      $owne = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
      

	$robi=substr($owne , 0, 2);
	
	
				
$resName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->name;

$fb = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->facebook;

	$mobile = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->phone;
	$amount = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->balance;
	$prebalance=$this->mit->accountBalance($canuserid); 


			
				
		
    $msg="Your $title_op request of Tk $amount for $mobile was successful.TrxID $trid. Your Update Balance $prebalance Tk.";
    
//$msg ="$title_op Tk $amount to $mobile Success for $trid Reaming BDT $prebalance";
    
	 $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){
	$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($owne,$msg); 
		     }else{	
			    $this->mdb->sms_send($owne,$msg);
		     }
  

}    
   
if($req_type=='apps'){
   $ftoken = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->note;
   if(!empty($ftoken)){
       
      $title ="$title_op Success $mobile"; 
  $this->mit->send_fcm_msg($title,$msg,$ftoken,$decideid); 
   }
  
     
 }   
      

if($req_type=='fb'){
$this->mdb->sendfb("flexiload","SMS*$fb*$msg*DD"); 
}

		print("<script>window.alert('Manual Success');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");

		}

		$data['id'] = $decideid;

		$this->load->view('admin/status/success', $data);


	}else if($para1=="resend") {


	if($_POST){
	$type=$var['type'];
	$number=$var['number'];
	$serviceid=$var['serviceid'];
	$amount=$var['amount'];
	$routes=$var['route'];

	$service = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->service;
	
	$rendomid = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->sid;
	$type = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->type;
	$pcode = $this->db->get_where('sendflexi',array('id' =>$decideid))->row()->pcode;
	if($serviceid==64 or $serviceid==16 or $serviceid==16384 or $serviceid==512){
	$firstThreeDigit=substr($number,0,3); 
	}else { 
	$firstThreeDigit=substr($number,0,2); 
	}

	$sql_rate="SELECT * from price where service='$service' and prefix='$firstThreeDigit' and type='$type' "; 
	$query = $this->db->query($sql_rate);
	  foreach ($query->result() as $raterow){
	  	$prcode=$raterow->pcode; 
	  }

	

	if($service==64 or $service==16 or $service==16384 or $service==512){

	$sql="UPDATE `sendflexi` SET `route` = '$routes', `balance` = '$amount', `type` = '$type', actual_send_time='$sucdate', idate='$idate', `resend_count` =resend_count+1, `status` = '0', `local` = '0' WHERE `sendflexi`.`id` ='$decideid'"; 
	
	}else {
	$sql="UPDATE `sendflexi` SET `route` = '$routes', `balance` = '$amount', `operator` = '$prcode', `type` = '$type', actual_send_time='$sucdate', idate='$idate', `resend_count` =resend_count+1, `status` = '0', `local` = '0' WHERE `sendflexi`.`id` ='$decideid'"; 
	}
 			

	$this->db->query($sql);
	
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Resend '.$number.' and '.$amount. ' By Admin';
		$this->mit->InsertActivetUser($uid,$msg);


	if($routes!="modem" or $routes!="manual") {

		$sql_api="SELECT * FROM `api_set` WHERE id='$routes' and status=1"; 
		$query = $this->db->query($sql_api);
	  	foreach ($query->result() as $row_api){
		

		$api_id=$row_api->id; 
		$provider=$row_api->provider; 
		$flapi_userid=$row_api->userid; 
		$flapi_key=$row_api->api_key; 

		$api_url=$row_api->url; 
		$apirespons=$row_api->response; 
		}
		
		 if($provider==11){
				    
				    if($pcode=="SK"){ $op='3';}else{
			$op=substr($firstThreeDigit, -1);
				 }
			$pdata = array(
				  "number" => $number,
				  "service" => 'MRC',
				  "amount" => $amount,
				  "number_type" => $type,
				  "refid" => $rendomid,
				  "access_id" => $flapi_userid,
				  "access_pass" => $flapi_key,
				   "operator" => $op
				);
				
				
			
					$url_send="http://$api_url/myportal/api/rechargeapi/recharge_api_thirdparty.php";
				
		$api_status=$this->mdb->sendPostData($url_send,$pdata);
				
					$apists=json_decode($api_status);
				$responsests = $apists->RECHARGE_STATUS;
				    
				        
				    }else{
		
		$postdata = array(
				  "number" => $number,
				  "service" => $service,
				  "amount" => $amount,
				  "type" => $type,
				  "id" => $rendomid,
				  "user" => $flapi_userid,
				  "key" => $flapi_key
				);
				
				$url_send ="http://".$api_url."/sendapi/request";
				
				
				
			$header=array(
    'api-key: '.$flapi_key.'',
    'api-user: '.$flapi_userid.''
);
				$api_status=$this->mdb->sendPostData($url_send,$postdata,$header);
			$apists=json_decode($api_status);
			$responsests = $apists->status;
			
				    }
				$apiupd = "Update sendflexi SET apiurl='$api_url', apiresponse='$api_status' where sid='$rendomid'";
						
			$this->db->query($apiupd);
				
			if($responsests==1 or $responsests=='RECEIVED') { 
            $apiupd = "Update sendflexi SET status='4', apiurl='$api_url', apiresponse='$api_status' where sid='$rendomid'";
						
			$this->db->query($apiupd);
						
            } 

	}


$modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
 if($modem==0 && $routes=="modem" ){
      $apiupd = "Update sendflexi SET status='0', local='0' where sid='$rendomid'";
						
			$this->db->query($apiupd);
     $this->mdb->real_time($decideid);
     
 }	



	print("<script>window.alert('Resend Successfuly');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");

	}

		$data['id'] = $decideid;

		$this->load->view('admin/status/resend', $data);

	}else if ($para1=="userShow") {

		$data['id'] = $decideid;

		$this->load->view('admin/status/userview', $data);

	}

		


	}
	
	
		public function otherservice($para1="", $para2="", $para3=""){
	
	
	$decideid = $this->mdb->passwordChanger('decrypt', $para2); 
	 $balance_system = $this->db->get_where('security_option',array('id' =>1))->row()->balance_system;
	 
	if($balance_system==1){
          
           $source='bank';
          
        }else{$source='main';}
	
	$sucdate=date('Y-m-d H:i:s'); 
	$idate=date('Y-m-d'); 
	$var=$this->input->post();	
	
	if($para1=="user_view") {
		
	$data['id'] = $decideid;
	$this->load->view('admin/others/user_view', $data);
		
	}else if($para1=="cancel_bank") {
	if($_POST) {
		
	$trid=$var['trid'];
	$refund=$var['refund'];
	
	$bstatus = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->status;
	
	$sql="UPDATE `bank_transfer` SET `trxid` = '$trid', `s_date` = '$idate', `update_req` = '$sucdate', `status` = '2',`refund` = '2' WHERE `bank_transfer`.`id` ='$decideid'"; 
	
	$this->db->query($sql);
	
	$sid = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->sid;
	
	$cansq="SELECT * FROM `trans` WHERE flex_id='$sid'"; 
	$query = $this->db->query($cansq);
	foreach ($query->result() as $row_cancel){
	$debitamt=$row_cancel->debit; 
	$canuserid=$row_cancel->userid;
	
	$prebalance=$this->mit->accountBalance($canuserid,$source); 
	$accountbalance=$prebalance+$debitamt; 
	
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
	
	$parent_id = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;
	
	$mobile = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->account;
	$amount = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->balance;
	
	$bank_name = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->bank_name;
		
	$bankop = $this->db->get_where('bank_name',array('bcode' =>$bank_name))->row()->bname;
	
	

	if($bstatus==0) {
	$cantypr="plus"; 
	$this->mit->balanceUpdate($canuserid,$debitamt,$cantypr,$source); 
    $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `send_sl`, `p_id`, `date`, `time`) VALUES (NULL, '$canuserid', 'Bank $bankop Cancell By Admin Num: $mobile and amount : $amount Res: $resellercustomerName', '$prebalance', '', '$debitamt', '$accountbalance', '$cantypr', '32', '$sid', '3', '$decideid', '$parent_id', '$idate', '$sucdate')";
    $this->db->query($sql_tr); 
	}	 
	}
	
	$user=$this->session->userdata('admin_session');
	$uid= $user->id;
	$msg= 'Bank : '.$bankop.' Cancell By Admin Num: '.$mobile. ' and amount : '.$amount.' Reseller : '.$resellercustomerName;
	$this->mit->InsertActivetUser($uid,$msg);

	print("<script>window.alert('Manual Cancell Successfuly');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");
		
	}
	$data['id'] = $decideid;
	$this->load->view('admin/others/cancel_bank', $data);
	}else if($para1=="manual_bank_confirm") {
	if($_POST) {
		
	$trid=$var['trid'];
	$refund=$var['refund'];
	
	$sql="UPDATE `bank_transfer` SET `trxid` = '$trid', `s_date` = '$idate', `update_req` = '$sucdate', `status` = '1',`refund` = '1' WHERE `bank_transfer`.`id` ='$decideid'"; 
	
	$this->db->query($sql);
	
	
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
	
	$parent_id = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;
	
	$mobile = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->account;
	$amount = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->balance;
	
	$bank_name = $this->db->get_where('bank_transfer',array('id' =>$decideid))->row()->bank_name;
		
	$bankop = $this->db->get_where('bank_name',array('bcode' =>$bank_name))->row()->bname;
	
	$msg= 'Bank : '.$bankop.' Success By Admin Num: '.$mobile. ' and amount : '.$amount.' Reseller : '.$resellercustomerName;
	$this->mit->InsertActivetUser($uid,$msg);
	
	print("<script>window.alert('Manual Successfuly');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");
		
	}
	$data['id'] = $decideid;
	$this->load->view('admin/others/manual_bank_confirm', $data);
		
	}else if($para1=="user_details_bank") {
	$data['id'] = $decideid;
	$this->load->view('admin/others/user_details_bank', $data);
	
	
	}else if($para1=="cancel_billpay") {
	
	if($_POST) {
	$trid=$var['trid'];
	$refund=$var['refund'];

	$bstatus = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->status;
		
	$sql="UPDATE `bill_pay` SET `trxid` = '$trid', `s_date` = '$idate', `update_req` = '$sucdate', `status` = '2', `refund` = '2' WHERE `bill_pay`.`id` ='$decideid'"; 
	
	$this->db->query($sql);
	
	$sid = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->sid;
	
	$cansq="SELECT * FROM `trans` WHERE flex_id='$sid'"; 
	$query = $this->db->query($cansq);
	foreach ($query->result() as $row_cancel){
	$debitamt=$row_cancel->debit; 
	$canuserid=$row_cancel->userid;
	
	$prebalance=$this->mit->accountBalance($canuserid,$source); 
	$accountbalance=$prebalance+$debitamt; 
	
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
	
	$parent_id = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;
	
	$mobile = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->account;
	$amount = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->balance;
	
	$bank_name = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->provider_name;
		
	$bankop = $this->db->get_where('billing_set',array('bill_code' =>$bank_name))->row()->name;
	


	if($bstatus==0) {
		
	$cantypr="plus"; 
	
	$this->mit->balanceUpdate($canuserid,$debitamt,$cantypr,$source); 
	
    $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `send_sl`, `p_id`, `date`, `time`) VALUES (NULL, '$canuserid', 'BillPay $bankop Cancell By Admin Num: $mobile and amount : $amount Res: $resellercustomerName', '$prebalance', '', '$debitamt', '$accountbalance', '$cantypr', '8', '$sid', '3', '$decideid', '$parent_id', '$idate', '$sucdate')";
	
    $this->db->query($sql_tr); 
	}	 
	}
	
	$user=$this->session->userdata('admin_session');
	$uid= $user->id;
	$msg= 'BillPay : '.$bankop.' Cancell By Admin Num: '.$mobile. ' and amount : '.$amount.' Reseller : '.$resellercustomerName;
	$this->mit->InsertActivetUser($uid,$msg);

	print("<script>window.alert('Manual Cancell Successfuly');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");
	
	}
	
	$data['id'] = $decideid;
	$this->load->view('admin/others/cancel_billpay', $data);

	}else if($para1=="manual_billpay_confirm") {
	
		if($_POST) {
		
	$trid=$var['trid'];
	$refund=$var['refund'];
	
	$sql="UPDATE `bill_pay` SET `trxid` = '$trid', `s_date` = '$idate', `update_req` = '$sucdate', `status` = '1',`refund` = '1' WHERE `bill_pay`.`id` ='$decideid'"; 
	
	$this->db->query($sql);
	
	
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
	
	$parent_id = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;
	
	$mobile = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->account;
	$amount = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->balance;
	
	$providername = $this->db->get_where('bill_pay',array('id' =>$decideid))->row()->provider_name;
		
	$bankop = $this->db->get_where('billing_set',array('bill_code' =>$providername))->row()->name;
	
	$msg= 'BillPay : '.$bankop.' Success By Admin Num: '.$mobile. ' and amount : '.$amount.' Reseller : '.$resellercustomerName;
	$this->mit->InsertActivetUser($uid,$msg);
	
	print("<script>window.alert('Manual Successfuly');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");
		
	}
	
	$data['id'] = $decideid;
	$this->load->view('admin/others/manual_billpay_confirm', $data);
		
	}else if($para1=="user_details_billpay") {
	$data['id'] = $decideid;
	$this->load->view('admin/others/user_details_billpay', $data);
	}
		
	
	
		
	}
	
	public function opreports() {
	$var=$this->input->post();	
	$varget=$this->input->get();

	if($_POST) {
	$resel=$var["resel"]; 
	$status=$var["status"]; 
	$operator=$var["operator"];
	$to1=$var["to1"];
	$from1=$var["from1"];
	}else {
	$resel=$varget["resel"]; 
	$status=$varget["status"]; 
	$operator=$varget["operator"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}
	$data['resel'] = $resel;
	$data['status'] = $status;
	$data['operator'] = $operator;
	$data['to1'] = $to1;
	$data['from1'] = $from1;

	
	$data['page_title'] = translate('operator_report');
	$data['page_name'] = 'operator_report';	
	$this->load->view('admin/index', $data);	
	}
	
	public function dailyreports() {
	$var=$this->input->post();	
	$varget=$this->input->get();

	if($_POST) {
	
	$todate=$var["todate"];
	}else {
	
	$todate=$varget["todate"];
	}
	
	$data['todate'] = $todate;

	
	$data['page_title'] = translate('daily_reports');
	$data['page_name'] = 'daily_reports';	
	$this->load->view('admin/index', $data);	
	}
	
	public function route_report() {
	$var=$this->input->post();	
	$varget=$this->input->get();

	if($_POST) {
	
	$to1=$var["to1"];
	$from1=$var["from1"];
	$routing=$var["routing"];
	$sim_to=$var["sim_to"];
	
	}else {
	
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	$routing=$varget["routing"];
	$sim_to=$varget["sim_to"];
	
	}
	
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['routing'] = $routing;
	$data['sim_to'] = $sim_to;

	
	$data['page_title'] = translate('route_report');
	$data['page_name'] = 'route_report';	
	$this->load->view('admin/index', $data);	
	}
	public function modemdevice($para1='',$para2='') {
		if(!empty($para1)){
	$this->db->where("id",$para1);
    $this->db->delete("modem_device");
          redirect('admin/modemdevice', 'refresh');
        }
	$data['page_title'] = translate('modem_Device');
	$data['page_name'] = 'modem_device';	
	$this->load->view('admin/index', $data);
		
	}
  
	public function modemlist($para1='',$para2='') {
		
	if($para1=='delete') {
	$time=time();	
	$timecheck_modem=$time-10;
		
	$sql2="DELETE from siminfo where time<$timecheck_modem and id='$para2'"; 
	$this->db->query($sql2);
	
	redirect('admin/modemlist', 'refresh');
		
	}
	$data['page_title'] = translate('modem_list');
	$data['page_name'] = 'modem_list';	
	$this->load->view('admin/index', $data);
		
	}
	public function modem_action($para1='') {
	
	
	$var=$this->input->post();	
	
	if($_POST) {
		
	$ip=$_SERVER['REMOTE_ADDR'];
	$time=time();
	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
	$sucdate=date('Y-m-d H:i:s');
	$idate=date('Y-m-d');
	
	$pin=$var['pin'];
	$status=$var['modsts'];
	$resid=$var['resid'];
	$modem_port=$var['sim'];
$number=$var['number'];
$bal=$var['bal'];
$tol=$var['tol'];
	$sql_modr="UPDATE `modem` SET `pin` = '$pin',`tolerance` = '$tol', `modem_port` = '$modem_port',`status` = '$status',`simbalance` = '$bal', `time` =  '$sucdate',`number` =  '$number' WHERE `modem`.`id` = '$resid'"; 
	$this->db->query($sql_modr);
	
	print("<script>window.alert('Update successfuly');</script>");
	print("<script>
   	window.opener.location.reload();
    window.close();
	</script>");
		
	}

	$data['id'] = $para1;	
	$data['page_title'] = translate('modem_action');
	//$data['page_name'] = 'others/modem_action';	
	$this->load->view('admin/others/modem_action', $data);
		
	}
	
	
	

		public function pending($page="", $from1="", $to1="", $resel="", $status="", $op="", $number="", $limit="")
	{
		

		$var=$this->input->post();
		$varget=$this->input->get();



		$action = isset($var["action"]) ? $var["action"] : null;
		$trid = isset($var["canclmsg"]) ? $var["canclmsg"] : null;
		$cnt = isset($var['id']) && is_array($var['id']) ? count($var['id']) : 0;

		if(isset($action)) {

		
		$create_date=date('Y-m-d H:i:s');
		$sucdate=date('Y-m-d H:i:s'); 
		$idate = date('Y-m-d');

		if($action=='resend') {$stsac='0';}
		if($action=='waitting') {$stsac='5';}
		if($action=='confirm') {$stsac='1';}
		if($action=='process') {$stsac='4';}
		if($action=='cancel')  {$stsac='3';}
		
		

		for($i=0;$i<$cnt;$i++) { 
		 $del_id=$var['id'][$i]; 
		 
		
		 
		 $canstatus = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->status;
			
		$multi_mobile_act = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->phone;
		
		$amount = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->balance;
		
		$service = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->service;
		
		$routes = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->route;
		$type = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->type;
		$rendomid = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->sid;
		
		$title_op = $this->db->get_where('module',array('serviceid' =>$service))->row()->title;
			
			
		 $query="UPDATE  `sendflexi` SET  `trxid` = '$trid', `status` =  '$stsac', `local` =  '$stsac', `idate` = '$idate', actual_send_time='$create_date' where id='$del_id'"; 
		    $this->db->query($query);
		    
		    
		    
		    
		    
		    
		    	if($action=='confirm') {
		    	    	$canuserid = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->userid;
		    	    
		    	    
		    	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;


	$mobile = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->phone;
	$amount = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->balance;
     	$robi=substr($resellercustomerName , 0, 2);

     $reamin=($prebalance+$debitamt);
    $msg="Your $title_op request of Tk $amount for $mobile was successful.TrxID $del_id. Your Update Balance $prebalance Tk.";
    
//$msg ="$title_op Tk $amount to $mobile Success.";


$fb = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->facebook; 	

	
   $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){
	$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($resellercustomerName,$msg); 
		     }else{	
			    $this->mdb->sms_send($resellercustomerName,$msg);
		     }
  

}

if($req_type=='apps'){
   $ftoken = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->note;
   if(!empty($ftoken)){
       
      $title ="$title_op Success $mobile"; 
  $this->mit->send_fcm_msg($title,$msg,$ftoken,$del_id); 
   }
  
     
 }   


}
		    
			
			if($action=='resend') {
				
			if($routes!="modem" or $routes!="manual") {

		$sql_api="SELECT * FROM `api_set` WHERE id='$routes' and status=1"; 
		$query = $this->db->query($sql_api);
	  	foreach ($query->result() as $row_api){
		

		$api_id=$row_api->id; 
		$provider=$row_api->provider; 
		$flapi_userid=$row_api->userid; 
		$flapi_key=$row_api->api_key; 

		$api_url=$row_api->url; 
		$apirespons=$row_api->response; 
		}
		
		$postdata = array(
				  "number" => $multi_mobile_act,
				  "service" => $service,
				  "amount" => $amount,
				  "type" => $type,
				  "id" => $rendomid,
				  "user" => $flapi_userid,
				  "key" => $flapi_key
				);
				
				$url_send ="http://".$api_url."/sendapi/request";
				
				
				
			  $header=array(
    'api-key: '.$flapi_key.'',
    'api-user: '.$flapi_userid.''
);
				$api_status=$this->mdb->sendPostData($url_send,$postdata,$header);
				
			$apists=json_decode($api_status);
			$responsests = $apists->status;
				
				
			if($responsests==1) { 
            $apiupd = "Update sendflexi SET status='4', apiurl='$api_url', apiresponse='$api_status' where sid='$rendomid'";
						
			$this->db->query($apiupd);
						
            } 

	}
	    
	    
	     $modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
 if($modem==0){
     $this->mdb->real_time($del_id);
     
 }				

		    
	    
	
	
	
	
	
	
			}

		     else if($action=='cancel') {



	if($canstatus!=3){
	
      
$amount = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->balance;
$service = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->service;
	
	$source = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->result;

      
	$cansq="SELECT * FROM `trans` WHERE send_sl='$del_id'"; 
	$query = $this->db->query($cansq);
	foreach ($query->result() as $row_cancel){
 	$debitamt=$row_cancel->debit; 
  	$canuserid=$row_cancel->userid; 
  	$prebalance=$this->mit->accountBalance($canuserid,$source); 
	$accountbalance=$prebalance+$debitamt; 
  	$cantypr="plus"; 
 
	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->username;
	$parent_id = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->p_id;

	$mobile = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->phone;
	$amount = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->balance;
	$sid333 = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->sid;
	$service = $this->db->get_where('sendflexi',array('id' =>$del_id))->row()->service;
	
	
$this->mit->balanceUpdate($canuserid,$debitamt,$cantypr,$source); 
      
    $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `status`, `send_sl`, `p_id`, `date`, `time`) VALUES (NULL, '$canuserid', '$title_op Cancell By Admin Num: $mobile and amount : $amount Res: $resellercustomerName', '$prebalance', '', '$debitamt', '$accountbalance', '$cantypr', '$service', '$sid333', '3', '$del_id', '$parent_id', '$idate', '$sucdate');";

     $this->db->query($sql_tr); 
     
     
     	$robi=substr($resellercustomerName , 0, 2);

     $reamin=($prebalance+$debitamt);
    $msg="Your $title_op request of Tk $amount for $mobile was Faild. Your Update Balance $reamin Tk.";
    
//$msg ="$title_op Tk $amount to $mobile faild  Reaming BDT $reamin";
$fb = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->facebook; 	

	
   $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){
	$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($resellercustomerName,$msg); 
		     }else{	
			    $this->mdb->sms_send($resellercustomerName,$msg);
		     }
  

}
     
if($req_type=='apps'){
   $ftoken = $this->db->get_where('reseller',array('id' =>$canuserid))->row()->note;
   if(!empty($ftoken)){
       
      $title ="$title_op faild $mobile"; 
  $this->mit->send_fcm_msg($title,$msg,$ftoken,$del_id); 
   }
  
     
 }        
     
     
     

 	} // trns table
 	} // cancel verify
} // cancel for
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= ucfirst($action). ' '.$title_op.'  Number '.$multi_mobile_act.' and Amount '.$amount;
		$this->mit->InsertActivetUser($uid,$msg);
		
		  }

	
		
		redirect($_SERVER['HTTP_REFERER']);

		}

		if($_POST){

		$resel=$var["resel"]; 
		$from1=$var["from1"]; 
		$to1=$var["to1"]; 
		$status=$var["status"]; 
		$op=$var["op"]; 
		$number=$var["number"]; 
		$limit=$var["limit"];
		} 


		else {

		$resel=$varget["resel"]; 
		$from1=$varget["from1"]; 
		$to1=$varget["to1"]; 
		$status=$varget["status"]; 
		$op=$varget["op"]; 
		$number=$varget["number"]; 
		$limit=$varget["limit"];
	}

		if(empty($limit) ) { 
		 $limit = 50; 
		 }
		  
		$link="from1=$from1&to1=$to1&resel=$resel&op=$op&status=$status&number=$number&limit=$limit"; 

		
		$targetpage = "?"; 
		 //your file name  (the name of this file) 
		 $pageselect=$varget['pageid']; 
		 //how many items to show per page 
		 $page = $varget['page']; 
		 if($page)  
		$start = ($page - 1) * $limit;       //first item to display on this page 
		 else 
		$start = 0;                //if no page var is given, set start to 0 
			 

		 if($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status=="all" and $number!=""){

		   $sql="select * from sendflexi where idate >= '$from1' and idate <= '$to1' and `phone` LIKE '%$number%' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc LIMIT $start, $limit"; 

		  $sqlcont="select * from sendflexi where idate >= '$from1' and idate <= '$to1' and `phone` LIKE '%$number%' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
			} 
		  /// only number search 
		  elseif($from1=="" and $to1=="" and $resel=="none" and $op=="all" and $status=="all" and $number!=""){ 
		    $sql="select * from sendflexi where `phone` LIKE '%$number%' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc LIMIT $start, $limit"; 

		     $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 

		  } 
		  
		  elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status!="all" and $number!=""){ 
		    $sql="select * from sendflexi where status='$status' and `phone` LIKE '%$number%' and idate >= '$from1' and idate <= '$to1' ORDER BY id desc LIMIT $start, $limit"; 

		     $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 

		  } 

		// oparetor 
		 elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status=="all" and $number=="") 
		  { 

		  $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and service like '%$op%' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4')  ORDER BY id desc LIMIT $start, $limit"; 

		  $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 

		  } 
		  /// userid search 
		  elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status=="all" and $number=="") 
		  { 


		  $sql="select * from sendflexi where userid='$resel' and idate >= '$from1' and idate <= '$to1' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc  LIMIT $start, $limit"; 
		  
		   $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		    
		  } 
		   
		  //date, service, user,// 
		  elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status=="all" and $number=="") 
		  { 


		  $sql="select * from sendflexi where userid='$resel' and service like '%$op%' and idate >= '$from1' and idate <= '$to1' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc  LIMIT $start, $limit"; 
		   
		    $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		  } 
		   
		   //date, user, status,// 
		  elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status!="all" and $number=="") 
		  { 


		  $sql="select * from sendflexi where userid='$resel' and status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 
		   $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		    
		  } 
		   
		  //date, service, status,// 
		  elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status!="all" and $number=="") 
		  { 


		  $sql="select * from sendflexi where status='$status' and service like '%$op%' and idate >= '$from1' and idate <= '$to1' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc  LIMIT $start, $limit"; 
		   $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		  
		  } 
		   
		   
		  elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status!="all" and $number=="") 
		  { 


		  $sql="select * from sendflexi where status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 
		    $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		   
		  } 
		   
		  
		   elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status!="all" and $number=="") 
		  { 

		  $sql="select * from sendflexi where userid='$resel' and idate >= '$from1' and idate <= '$to1' and service like '%$op%' and status='$status' order by id desc LIMIT $start, $limit"; 
		  
		    $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		   
		  } 
		   
		   elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status!="all" and $number!="") 
		  { 

		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and userid='$resel' and idate >= '$from1' and idate <= '$to1' and service like '%$op%' and status='$status' order by id desc LIMIT $start, $limit"; 
		   
		  
		   $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		  
		  } 
		   
		   elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status!="all" and $number!="") 
		  { 

		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and userid='$resel' and idate >= '$from1' and idate <= '$to1'  and status='$status' order by id desc LIMIT $start, $limit"; 
		   
		  $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		  
		  } 
		   
		   
		   elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status=="all" and $number!="") 
		  { 

		  $sql="select * from sendflexi where `phone` LIKE '%$number%' and userid='$resel' and idate >= '$from1' and idate <= '$to1' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc LIMIT $start, $limit"; 
		   
		  $sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		  
		  } 
		   
		    elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status=="all" and $number!="") 
		  { 

		  $sql="select * from sendflexi where `phone` LIKE '%$number%'  and service like '%$op%' and idate >= '$from1' and idate <= '$to1' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc LIMIT $start, $limit";
		   
 		$sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
		  
		  } 
		   
		  //sssss 
		   
		   
		  elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status=="all" and $number=="") 
		  { 
		  $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') ORDER BY id desc  LIMIT $start, $limit"; 

		    $sqlcont="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') ORDER BY id desc"; 
		  

		  
		 }else { 
	  if(!empty($op)) {
		  
		$sql="select * from sendflexi where service='$op' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc LIMIT $start,$limit"; 

		$sqlcont="select * from sendflexi where service='$op' and (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
			
		  
		}else {
		  
		$sql="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc LIMIT $start,$limit"; 

		$sqlcont="select * from sendflexi where (local='0' or local='4' or local='5' or local='2') and (status='0' or status='5' or status='2' or status='4') order by id desc"; 
			
	  }
	}
			


		  $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
		  } 

		$data['number'] = $number;
		$data['resel'] = $resel;
		$data['op'] = $op;

		$data['status'] = $status;
		$data['limit'] = $limit;
		$data['to1'] = $to1;
		$data['from1'] = $from1; 

		$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);
		
		$query2 = $this->db->query($sql);

		$data['links'] = $pagination;
		$data['pending_history'] = $query2->result();

		$data['page_title'] = 'Pending';

		
		$data['page_name'] = 'pending';
		$this->load->view('admin/index', $data);

	}
	public function history($serv="", $page="", $from1="", $to1="", $resel="", $status="", $op="", $number="", $limit="")
	{

		$idate = date('Y-m-d');

		$var=$this->input->post();	
		$varget=$this->input->get();	

		if($_POST){

		$resel=$var["resel"]; 
		$from1=$var["from1"]; 
		$to1=$var["to1"]; 
		$status=$var["status"]; 
		$op=$var["op"]; 
		$number=$var["number"]; 
		$limit=$var["limit"];

/*
		$hitory_data = array(
               'resel' => $resel,
               'from1' => $from1,
               'to1' => $to1,
               'status' => $status,
               'op' => $op,
               'number' => $status,
               'limit'     => $limit  
             );

  // $this->session->set_userdata('historylog',$hitory_data);
  */

		} 


		else {

//	$varget= $this->session->userdata('historylog');
	
		$resel=$varget["resel"]; 
		$from1=$varget["from1"]; 
		$to1=$varget["to1"]; 
		$status=$varget["status"]; 
		$op=$varget["op"]; 
		$number=$varget["number"]; 
		$limit=$varget["limit"];

		
	}

		if(empty($limit) ) { 
		 $limit = 50; 
		 }

		  
		$link="from1=$from1&to1=$to1&resel=$resel&op=$op&status=$status&number=$number&modnumber=$modnumber&limit=$limit"; 
		
		$targetpage = "?"; 
		 //your file name  (the name of this file) 
		 $pageselect=$varget['pageid']; 
		 //how many items to show per page 
		 $page = $varget['page']; 
		 if($page)  
		$start = ($page - 1) * $limit;       //first item to display on this page 
		 else 
		$start = 0;                //if no page var is given, set start to 0 


	 // only number 
    if($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status=="all" and $number!="" and $modnumber==""){ 
    $sql="select * from sendflexi where idate >= '$from1' and idate <= '$to1' and (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') order by id desc LIMIT $start, $limit"; 

     $sqlcont="select * from sendflexi where idate >= '$from1' and idate <= '$to1' and (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') order by id desc"; 
 
  } 
   
// only service 

 elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status=="all" and $number=="" and $modnumber=="") 
  { 

  $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and service='$op'  ORDER BY id desc LIMIT $start, $limit"; 

   $sqlcont="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' and service='$op'  ORDER BY id desc"; 

  
  } 
  // only userid 
  elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status=="all" and $number=="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 

   $sqlcont="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc"; 
  
  } 
  // only status 
   
   elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status!="all" and $number=="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 

   $sqlcont="select * from sendflexi where status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
   
  } 
   
  
  //date, service, user,// 
  elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status=="all" and $number=="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 



   $sqlcont="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc "; 
  
  } 
   
   //date, user, status,// 
  elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status!="all" and $number=="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 

  $sqlcont="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and status='$status' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
  
  } 
   
  //date, service, status,// 
  elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status!="all" and $number=="" and $modnumber=="") 
  { 


  $sql="select * from sendflexi where status='$status' and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 

   $sqlcont="select * from sendflexi where status='$status' and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
   
  } 
   
  // service and user and number 
  elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status=="all" and $number!="" and $modnumber=="") 
  { 


  $sql="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 

   $sqlcont="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
  
  } 
  
   
  
   elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status!="all" and $number=="" and $modnumber=="" ) 
  { 

  $sql="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc LIMIT $start, $limit"; 

   $sqlcont="select * from sendflexi where (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc"; 
  
  } 
   
   
  elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status=="all" and $number=="" and $modnumber=="") 
  { 
  $sql="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' ORDER BY id desc  LIMIT $start, $limit"; 
  
$sqlcont="SELECT * FROM sendflexi WHERE idate >= '$from1' and idate <= '$to1' ORDER BY id desc "; 

  } 
   
   elseif($from1!="" and $to1!="" and $resel!="none" and $op!="all" and $status!="all" and $number!="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc LIMIT $start, $limit";

  $sqlcont="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' and service='$op' and status='$status' order by id desc "; 
   
  
  } 
   
   elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status!="all" and $number!="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1'  and status='$status' order by id desc LIMIT $start, $limit"; 

   $sqlcont="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1'  and status='$status' order by id desc"; 
   
  
  
  } 
   
   
   elseif($from1!="" and $to1!="" and $resel!="none" and $op=="all" and $status=="all" and $number!="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start, $limit"; 
   
   $sqlcont="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and (userid='$resel' or level='$resel' or level2='$resel' or level3='$resel' or level4='$resel' or level5='$resel') and idate >= '$from1' and idate <= '$to1' order by id desc"; 
   
  
  
  } 
   
    elseif($from1!="" and $to1!="" and $resel=="none" and $op!="all" and $status=="all" and $number!="" and $modnumber=="") 
  { 

  $sql="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start, $limit"; 
   
  
 $sqlcont="select * from sendflexi where (`send_number` LIKE '%$number%' or `phone` LIKE '%$number%') and service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
   
  
  } 
  
   elseif($from1!="" and $to1!="" and $resel=="none" and $op=="all" and $status=="all" and $number=="" and $modnumber!="") 
  { 

  $sql="select * from sendflexi where `send_number` LIKE '%$modnumber%' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start, $limit"; 
   
  $sqlcont="select * from sendflexi where `send_number` LIKE '%$modnumber%' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
   

  } 

  else 
  { 

  	$idate=date('Y-m-d');

   
		if($serv=="all") { 

		$sql="SELECT * from sendflexi where idate='$idate' order by id desc LIMIT $start,$limit"; 
		$sqlcont="SELECT * from sendflexi where idate='$idate' order by id desc"; 
		}else { 
		$op = $this->db->get_where('module',array('title' =>$serv))->row()->serviceid;

		$sql="SELECT * from sendflexi where service='$op' and idate='$idate' order by id desc LIMIT $start,$limit"; 
		$sqlcont="SELECT * from sendflexi where service='$op' and idate='$idate' order by id desc"; 	

		} 
		} /// hsitory


 $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
		  }  

	$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);
		
		$query2 = $this->db->query($sql);

		$data['number'] = $number;
		$data['resel'] = $resel;
		$data['op'] = $op;

		$data['status'] = $status;
		$data['limit'] = $limit;

		$data['to1'] = $to1;
		$data['from1'] = $from1;

		$servicettile = $this->db->get_where('module',array('serviceid' =>$op))->row()->title;

		$data['links'] = $pagination;
		$data['recharge_history'] = $query2->result();

		$data['page_title'] = $servicettile. ' History';

		$data['page_name'] = 'history_list';
		$this->load->view('admin/index', $data);
	}

	public function smsInbox($serv="", $page="", $from1="", $to1="", $op="", $number="", $limit="") {


		$var=$this->input->post();	
		$varget=$this->input->get();	

		if($_POST){

		
		$from1=$var["from1"]; 
		$to1=$var["to1"]; 
		
		$op=$var["op"]; 
		$number=$var["number"]; 
		$limit=$var["limit"];
		$opcompany=$var["opcompany"]; 
		
		} 


		else {

		
		$from1=$varget["from1"]; 
		$to1=$varget["to1"]; 
		
		$op=$varget["op"]; 
		$number=$varget["number"]; 
		$limit=$varget["limit"]; 
		$opcompany=$varget["opcompany"]; 
	}

	
		if(empty($limit) ) { 
		 $limit = 50; 
		 }

		 $link="from1=$from1&to1=$to1&number=$number&op=$op&opcompany=$opcompany&limit=$limit"; 

		 $targetpage = "?"; 
		 //your file name  (the name of this file) 
		 $pageselect=$varget['pageid']; 
		 //how many items to show per page 
		 $page = $varget['page']; 
		 if($page)  
		$start = ($page - 1) * $limit;       //first item to display on this page 
		 else 
		$start = 0;                //if no page var is given, set start to 0 
		

	if($from1!="" && $to1!="" && $number!="" && $op=="all"){ 

    $sql="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start,$limit"; 

    $sqlcont="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and idate >= '$from1' and idate <= '$to1' order by id desc"; 
 	} 
   
	else if($from1!="" && $to1!="" && $number!="" && $op!="all") { 
			$sql="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and servicid='$op' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start,$limit"; 

			$sqlcont="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and servicid='$op' and idate >= '$from1' and idate <= '$to1' order by id desc"; 

			  
	} 

			  
	else if($from1!="" && $to1!="" && $number!="" && $op!="all") { 

			$sql="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and servicid='$op' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start,$limit";

			$sqlcont="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and servicid='$op' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
		

// new update 12/07/2017 telco search	
		}else if($from1!="" && $to1!="" && $number=="" && $op!="all" && $opcompany!="all") { 

			$sql="select * from reportserver where servicid='$op' and company='$opcompany' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start,$limit";

			$sqlcont="select * from reportserver where servicid='$op' and company='$opcompany' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
			
		}else if($from1!="" && $to1!="" && $number!="" && $op!="all" && $opcompany!="all") { 

			$sql="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and servicid='$op' and company='$opcompany' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start,$limit";

			$sqlcont="select * from reportserver where (`msgbody` LIKE '%$number%' or `ownNumber` LIKE '%$number%') and servicid='$op' and company='$opcompany' and idate >= '$from1' and idate <= '$to1' order by id desc"; 

			  
	}else if($from1!="" && $to1!="" && $number=="" && $op=="all") { 

			$sql="select * from reportserver where idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start,$limit"; 

			$sqlcont="select * from reportserver where idate >= '$from1' and idate <= '$to1' order by id desc "; 
			 
			} 

			else if($from1!="" && $to1!="" && $number=="" && $op!="all") { 

			$sql="select * from reportserver where servicid like '%$op%' and idate >= '$from1' and idate <= '$to1' order by id desc LIMIT $start,$limit";

			$sqlcont="select * from reportserver where servicid like '%$op%' and idate >= '$from1' and idate <= '$to1' order by id desc"; 


			}else { 

			if($serv=="all"){ 
			 $sql="select * from reportserver order by id desc LIMIT $start,$limit"; 

			 $sqlcont="select * from reportserver order by id desc";

			 }else{ 

			 $op = $this->db->get_where('module',array('title' =>$serv))->row()->serviceid;

			 $sql="select * from reportserver where servicid = '$op' order by id desc LIMIT $start,$limit";

			 $sqlcont="select * from reportserver where servicid = '$op' order by id desc";

			} 
			 } 

		$querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
		  } 

		 $pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);

		$query2 = $this->db->query($sql);

		$data['number'] = $number;
		$data['resel'] = $resel;
		$data['op'] = $op;
		$data['opcompany'] = $opcompany;
		
		

		$data['status'] = $status;
		$data['limit'] = $limit;

		$data['start'] = $start;

		$data['to1'] = $to1;
		$data['from1'] = $from1;

		$data['total_pagescount'] = $total_pages;

		$servicettile = $this->db->get_where('module',array('serviceid' =>$op))->row()->title;

		$data['links'] = $pagination;
		$data['inbox_history'] = $query2->result();




		$data['page_title'] = $servicettile.' Message Inbox';

		$data['page_name'] = 'sms_inbox';
		$this->load->view('admin/index', $data);


	}
	
	public function cardHistory() {
		
	$var=$this->input->post();	
	$varget=$this->input->get();	
	
		if($_POST){
		$from1=$var["from1"]; 
		$to1=$var["to1"]; 
		$op=$var["op"]; 
		$operator=$var["operator"]; 
		$limit=$var["limit"];
		}else {
		$from1=$varget["from1"]; 
		$to1=$varget["to1"];
		$op=$varget["op"]; 
		$operator=$varget["operator"]; 
		$limit=$varget["limit"]; 
		}

		$data['resel'] = $resel;
		$data['operator'] = $operator;
		$data['limit'] = $limit;
		$data['start'] = $start;
		$data['to1'] = $to1;
		$data['from1'] = $from1;
		
		
		$data['page_title'] = ' Card History ';

		$data['page_name'] = 'cardHistory';
		$this->load->view('admin/index', $data);
	}
	
	public function card($para1="", $para2="", $para3="") {
		
		$var=$this->input->post();	
		$varget=$this->input->get();
		$create_date = date('Y-m-d H:i:s');
		$idate = date('Y-m-d');
		if($para1=="add") {
			if($_POST) {
			
			$serial=$var['serial'];	
			$PIN=$var['PIN'];
			$amount=$var['amount'];
			$cost=$var['cost'];
			$op=$var['op'];
			$type=$var['type'];			
			$exp_date=$var['exp_date'];	
				
			$sql="INSERT INTO `refil_card` (`serial`, `PIN`, `amount`, `cost`, `op`, `type`, `status`, `add_date`, `exp_date`, `date`) VALUES ('$serial', '$PIN', '$amount', '$cost', '$op', '$type', '0', '$create_date', '$exp_date', '$idate')"; 

			$this->db->query($sql);
			redirect('admin/card/', 'refresh');

			}
		$data['page_title'] = ' Card Add ';
		$data['page_name'] = 'card_add';	
		}else if($para1=="soldcard") {
			
		$slodcard= "UPDATE  `refil_card` SET  `status` =  '2' WHERE  `refil_card`.`id` ='$para2'";
		$this->db->query($slodcard);
		
		}else if($para1=="deletecard") {
			
		$delcard= "DELETE FROM refil_card WHERE id='$para2'"; 
		$this->db->query($delcard);
			
		}else if($para1=='opstatus'){
			
		$upsql="UPDATE `op_card` SET `status` = '$para2' WHERE `op_card`.`id` = '$para3'";
		
		$this->db->query($upsql);
		
		redirect('admin/cardop', 'refresh');
			
		}else {
			
		redirect('admin/cardManage', 'refresh');
		
		}
		$this->load->view('admin/index', $data);	
	}
	
	public function cardop($para1="", $para2="", $para3=""){
		
		
		$var=$this->input->post();	
		$create_date=date('Y-m-d H:i:s');
	
		if($_POST) {
		
		$code = $var['code'];
		$prefix = $var['prefix'];
		$operatorname = $var['operatorname'];
		$slid = $var['slid'];
		
		$query="select count(*) as cnt from op_card where name='$operatorname' and prefix='$prefix' and card_code='$code'";
		
		$query23 = $this->db->query($query);
		foreach ($query23->result() as $row) {
			$count=$row->cnt; 
			}
		if($count>0){ 
		redirect('admin/cardop');
		
		} 

		else{ 

		if(!empty($slid)) {

		$upsqld="UPDATE `op_card` SET `name` = '$operatorname', `prefix` = '$prefix', `card_code` = '$code' WHERE `op_card`.`id` = '$slid'";
		
		$this->db->query($upsqld);
		redirect('admin/cardop');

		}else {
		
		$sql="INSERT INTO `op_card` (`name`, `prefix`, `card_code`, `status`, `date`) VALUES ('$operatorname', '$prefix', '$code', '1', '$create_date');"; 
		
		$this->db->query($sql);
		}
		}
		
		redirect('admin/cardop');
			
		}else if($para1=="edit") {
		$data['page_title'] = ' Edit Card Oparetor';
		$data['page_name'] = 'card_oparetor';
		$data['eid'] = $para2;
		}else if($para1=="update"){
		}else {
		$data['page_title'] = ' Add Card Oparetor';
		$data['page_name'] = 'card_oparetor';
		}
		$this->load->view('admin/index', $data);	
	}
	
	public function cardManage(){
	
	
	$var=$this->input->post();	
	$varget=$this->input->get();
	
	if($_POST) {
	$serial=$var["serial"]; 
	$status=$var["status"]; 
	$op=$var["op"]; 
	$pin=$var["pin"];
	$limit=$var["limit"];
	$to1=$var["to1"];
	$from1=$var["from1"];
	}else {
	$serial=$varget["serial"]; 
	$status=$varget["status"]; 
	$op=$varget["op"]; 
	$pin=$varget["pin"];
	$limit=$varget["limit"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}
	
	$data['serial'] = $serial;
	$data['status'] = $status;
	$data['op'] = $op;
	$data['pin'] = $pin;
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['limit'] = $limit;
	
	$data['page_title'] = ' Card Management ';
	$data['page_name'] = 'card_manage';	
	$this->load->view('admin/index', $data);	

	}
	public function bankpending() {
	
	$var=$this->input->post();	
	$varget=$this->input->get();
	
	

	if($_POST) {
	$resel=$var["resel"]; 
	$status=$var["status"]; 
	$op=$var["op"]; 
	$number=$var["number"];
	$limit=$var["limit"];
	
	$to1=$var["to1"];
	$from1=$var["from1"];
	
	
	}else {
	$resel=$varget["resel"]; 
	$status=$varget["status"]; 
	$op=$varget["op"]; 
	$number=$varget["number"];
	$limit=$varget["limit"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}
	
	$data['resel'] = $resel;
	$data['status'] = $status;
	$data['op'] = $op;
	$data['number'] = $number;
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['limit'] = $limit;
	
	$data['page_title'] = 'Bank Pending';
	$data['page_name'] = 'bankPending';	
	$this->load->view('admin/index', $data);	
	}
	
	public function bankhistory() {
	
	$var=$this->input->post();	
	$varget=$this->input->get();
	
	

	if($_POST) {
	$resel=$var["resel"]; 
	$status=$var["status"]; 
	$op=$var["op"]; 
	$number=$var["number"];
	$limit=$var["limit"];
	
	$to1=$var["to1"];
	$from1=$var["from1"];
	
	
	}else {
	$resel=$varget["resel"]; 
	$status=$varget["status"]; 
	$op=$varget["op"]; 
	$number=$varget["number"];
	$limit=$varget["limit"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}
	
	$data['resel'] = $resel;
	$data['status'] = $status;
	$data['op'] = $op;
	$data['number'] = $number;
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['limit'] = $limit;
	
	$data['page_title'] = 'Bank History';
	$data['page_name'] = 'bankhistory';	
	$this->load->view('admin/index', $data);	
	}
	
	public function bankset($para1="", $para2="", $para3="") {
	
	
	$var=$this->input->post();	
	$create_date=date('Y-m-d H:i:s');
	
	if($_POST) {
		
		$bcode = $var['bcode'];
		$swift = $var['swift'];
		$operatorname = $var['operatorname'];
		$slid = $var['slid'];
		
	$query="select count(*) as cnt from bank_name where bname='$operatorname' and bcode='$bcode' and swift='$swift'";
	
	$query23 = $this->db->query($query);
	foreach ($query23->result() as $row) {
		$count=$row->cnt; 
		}
	if($count>0){ 
	redirect('admin/bankset');
	
	} 

	else{ 

	if(!empty($slid)) {

	$upsqld ="UPDATE `bank_name` SET `bname` = '$operatorname', `bcode` = '$bcode', `swift` = '$swift' WHERE `bank_name`.`id` = '$slid'";
	$this->db->query($upsqld);
	redirect('admin/bankset');

	}else {

	$sql="INSERT INTO `bank_name` (`bname`, `bcode`, `swift`, `status`, `date`) VALUES ('$operatorname', '$bcode', '$swift', '1', '$create_date')"; 
	$this->db->query($sql);
	}
	}
	
	redirect('admin/bankset');
		
	}else if($para1=="edit") {
	
	$data['page_title'] = 'Bank Setting Edit';
	$data['page_name'] = 'banksettings';	
	$data['eid']= $para2;
	}else if($para1=="update"){
		
	}else if($para1=="status") {
	

	$upsql="UPDATE `bank_name` SET `status` = '$para2' WHERE `bank_name`.`id` = '$para3'";
	$this->db->query($upsql);
	
	redirect('admin/bankset', 'refresh');
	
	}else {
	$data['page_title'] = 'Bank Settings';
	$data['page_name'] = 'banksettings';
	}
	$this->load->view('admin/index', $data);	
	}
	
	/// bill pay start here
	
	public function billpaypending() {
	
	$var=$this->input->post();	
	$varget=$this->input->get();

	if($_POST) {
	$resel=$var["resel"]; 
	$status=$var["status"]; 
	$op=$var["op"]; 
	$number=$var["number"];
	$limit=$var["limit"];
	
	$to1=$var["to1"];
	$from1=$var["from1"];
	
	
	}else {
	$resel=$varget["resel"]; 
	$status=$varget["status"]; 
	$op=$varget["op"]; 
	$number=$varget["number"];
	$limit=$varget["limit"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}
	
	$data['resel'] = $resel;
	$data['status'] = $status;
	$data['op'] = $op;
	$data['number'] = $number;
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['limit'] = $limit;
	
	$data['page_title'] = 'BillPay Pending';
	$data['page_name'] = 'billPending';	
	$this->load->view('admin/index', $data);	
	}
	
	public function billpayhistory() {
	
	$var=$this->input->post();	
	$varget=$this->input->get();

	if($_POST) {
	$resel=$var["resel"]; 
	$status=$var["status"]; 
	$op=$var["op"]; 
	$number=$var["number"];
	$limit=$var["limit"];
	
	$to1=$var["to1"];
	$from1=$var["from1"];
	
	
	}else {
	$resel=$varget["resel"]; 
	$status=$varget["status"]; 
	$op=$varget["op"]; 
	$number=$varget["number"];
	$limit=$varget["limit"];
	$to1=$varget["to1"];
	$from1=$varget["from1"];
	}
	
	$data['resel'] = $resel;
	$data['status'] = $status;
	$data['op'] = $op;
	$data['number'] = $number;
	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['limit'] = $limit;
	
	$data['page_title'] = 'BillPay History';
	$data['page_name'] = 'billpayhistory';	
	$this->load->view('admin/index', $data);	
	}
	
	
	//billpay settings
	
	public function billpaymng($para1="", $para2="", $para3="") {
	
	
	$var=$this->input->post();	
	$create_date=date('Y-m-d H:i:s');
	
	if($_POST) {
		
		
		$bill_code = $var['bill_code'];
		$operatorname = $var['operatorname'];
		$slid = $var['slid'];
		
	$query="select count(*) as cnt from billing_set where name='$operatorname' and bill_code='$bill_code'";
	
	$query23 = $this->db->query($query);
	foreach ($query23->result() as $row) {
		$count=$row->cnt; 
		}
	if($count>0){ 
	redirect('admin/billpaymng');
	
	} 

	else{ 

	if(!empty($slid)) {

	$upsqld="UPDATE `billing_set` SET `name` = '$operatorname', `bill_code` = '$bill_code' WHERE `billing_set`.`id` = '$slid'";

	$this->db->query($upsqld);
	
	redirect('admin/billpaymng');

	}else {
		
	$bilsetinser = array(
	'name'=>$operatorname,
	'bill_code'=>$bill_code,
	'status'=>1, 
	'date'=>$create_date
	);
	
	$this->db->insert('billing_set', $bilsetinser);

	//$sqls="INSERT INTO `billing_set` (`name`, `bill_code`, `status`, `date`) VALUES ('$operatorname', '$bill_code' '1', '$create_date')";
	
	//$this->db->query($sqls);
	}
	}
	
	redirect('admin/billpaymng');
		
	}else if($para1=="edit") {
	
	$data['page_title'] = 'BillPay Setting Edit';
	$data['page_name'] = 'billsettings';	
	$data['eid']= $para2;
	}else if($para1=="update"){
		
	}else if($para1=="status") {
	$upsql="UPDATE `billing_set` SET `status` = '$para2' WHERE `billing_set`.`id` = '$para3'";
	$this->db->query($upsql);
	redirect('admin/billpaymng', 'refresh');
	
	}else {
	$data['page_title'] = 'BillPay Settings';
	$data['page_name'] = 'billsettings';
	}
	$this->load->view('admin/index', $data);	
	}
	
	//billpay finish
	
	

	 public function adminPayment($para="") {
		 
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$parent= $user->p_id;
		
		$residhex=$this->mdb->passwordChanger('decrypt', $para);
		$query2 = $this->db->query("select * from reseller where id='$residhex'");
		$row = $query2->row();
		$memid = $row->username;
		
		$data['resid'] = $residhex;
		
		if($this->input->post()){
		
   	 	$var=$this->input->post();
		$description  = $var['description'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$main         = $var['main'];
		
		$this->form_validation->set_rules('amount', 'Amount Not Work', 'numeric|greater_than[0.99]|required|xss_clean');
		
		 if ($this->form_validation->run() == FALSE)
                {
		$this->session->set_flashdata('error', 'Amount Not Work');
		$this->load->view('admin/status/add_pay', $data);
				}else {
					
		//$data['page_title'] = $memid.' Payment';
		//$data['page_name'] = 'payment_review';
		$this->load->view('admin/status/payment_review', $data);
				}

		}else {
		$this->load->view('admin/status/add_pay', $data);
		//$data['page_title'] = $memid.' Payment';
		//$data['page_name'] = 'payment';
		}
		
		 
	 }

	  public function paymentAction() {
		  
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$sendername = $user->username;
		$uparent = $user->p_id;
		
		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A'); 
		$ip = $_SERVER['REMOTE_ADDR']; 

		$trxid=uniqid(rand(1111,9999),true); 
		
		  
		if($this->input->post()){
		
   	 	$var=$this->input->post();
		$description  = $var['description'];
		$amount       = $var['amount'];
		$type         = $var['type'];
		$ucid         = $var['ucid'];
	$source        = $var['main'];
	
		$pincode         = $var['pincode'];	
		$admintyp='admin';

		$otppin = $this->mit->otpchk($pincode,$admintyp);

		if($otppin) {
		

		$query = $this->db->query("select * from reseller where p_id='$uid'");
		foreach ($query->result() as $row)
					{
						$myresbal += $row->balance;
					}
				
		  
		$balancead = $cbalance - $myresbal;


		if($type=="Transfer") { 

		$prebalance=$this->mit->accountBalance($ucid,$source); 

		$t="plus"; 
		$balchange = $this->mit->balanceUpdate($ucid,$amount,$t,$source); 
		if($balchange) {

		$bal=$this->mit->accountBalance($ucid,$source); 

		$direct_parent = $this->db->get_where('reseller',array('id' =>$ucid))->row()->p_id;
		
		$resellri = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;

		 $sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `service`, `flex_id`, `p_id`, `date`, `time`) VALUES (NULL, '$ucid', 'Deposit By admin $description', '$prebalance', '0', '$amount', '$bal', 'plus', 'transfer', '$trxid', '$direct_parent', '$idate', '$create_date')"; 

		 $this->db->query($sql_tr);

		$sql="INSERT INTO `pay_receive` (`id`, `trxid`, `userid`, `desc`, `remark`, `sender`, `p_id`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$trxid', '$ucid', 'Deposit By admin $description', 'By Admin', 'Admin', '$direct_parent', '$amount', '$bal', '$type', 'recharge', '$idate', '$create_date')";

		$this->db->query($sql);
		
		$robi=substr($resellri, 0, 2);
		
		 $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  

$getuser = $this->db->get_where('reseller',array('id' =>$ucid))->row()->name;
			$getusers = $this->db->get_where('reseller',array('id' =>$uid))->row()->name;
			
	//	$msg="Your $source balance update by $amount new $source Balance $bal $description";
		
			$msg="You have received $source Balance $amount Tk, Your Update $source Balance $bal Tk.";
		
          
if($sents=='1'){
	$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($resellri,$msg); 
		     }else{	
			    $this->mdb->sms_send($resellri,$msg);
		     }
  

}    



   $ftoken = $this->db->get_where('reseller',array('id' =>$ucid))->row()->note;
   if(!empty($ftoken)){
       
      $title ="Received $source balance $amount TK"; 
  $this->mit->send_fcm_msg($title,$msg,$ftoken,$ucid); 
   }
  
     
        
    

      
		$ank_fb = $this->db->get_where('reseller',array('id' =>$ucid))->row()->facebook;
		if(!empty($ank_fb)){
  $this->mdb->sendfb("flexiload","SMS*$ank_fb*$msg*DD"); 	
        }   
		
		

		$msg= 'Payment Add Reseller: '.$resellri.' Amount: '.$amount;
		$this->mit->InsertActivetUser($uid,$msg);

		print("<script>window.alert('Payment added successfuly');</script>"); 
		print("<script> 
		   window.opener.location.reload(); 
		    window.close(); 
		</script>"); 
		}
	}else {

		$crntbal=$this->mit->accountBalance($ucid,$source); 

		if($crntbal>=$amount) {

		$t="minus"; 
		$this->mit->balanceUpdate($ucid, $amount,$t,$source); 

		$bal=$this->mit->accountBalance($ucid,$source); 

		$direct_parent = $this->db->get_where('reseller',array('id' =>$ucid))->row()->p_id;
		
		$resellri = $this->db->get_where('reseller',array('id' =>$ucid))->row()->username;
		

		$sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `flex_id`, `p_id`,  `date`, `time`) VALUES (NULL, '$ucid', 'Deposit  Return By admin $description', '$crntbal', '$amount', '', '$bal', 'minus', '$trxid', '$direct_parent', '$idate', '$create_date')";

		$this->db->query($sql_tr); 



		$sql="INSERT INTO `pay_receive` (`id`, `userid`, `trxid`, `desc`, `remark`, `sender`, `p_id`, `debit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$ucid', '$trxid', '$description', 'By Admin', 'Admin', '$direct_parent', '$amount', '$bal', '$type', 'recharge', '$idate', '$create_date')";


		$this->db->query($sql);
		
		
		$msg= 'Payment Return Reseller: '.$resellri.' Amount: '.$amount;
		$this->mit->InsertActivetUser($uid,$msg);

		print("<script>window.alert('Payment Return successfuly');</script>"); 
		print("<script> 
		     window.opener.location.reload(); 
		    window.close(); 
		</script>"); 

	}else {

		print("<script>window.alert('Payment Return Not Work Balance Is Down');</script>"); 
		print("<script> 
		     window.opener.location.reload(); 
		    window.close(); 
		</script>"); 


	}

	}


	}// pin chk 
	 {
	 	print("<script>window.alert('Incorrect Transaction PIN');</script>");
		print("<script>
		   window.opener.location.reload();
		    window.close();
		</script>");


	}


	
	} // post finish



		  
	}
	 
	public function changeSecurelogin($memid = NULL)
	{
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;

	$decideid = $this->mdb->passwordChanger('decrypt', $memid); 
	
	if($decideid==1) {
		
		print("<script>window.alert('Not Access');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");
	}

	if($this->input->post()){

	$var=$this->input->post();

	$pin = $var['pincode'];

	$typ = 'admin';

	$otpchking = $this->mit->otpchk($pin,$typ);

	if($otpchking) {

	$chnagetype = $var['chnagetype'];

	if($chnagetype=="password") {
	$change = array(
	"password" => $this->mdb->generate($var['change'])
	);
	}else {
	$change = array(
	"pincode" => $this->mdb->generate($var['change'])
	);
	}
    $this->mdb->update('reseller',$change,array('id'=>$decideid));
	
	$reselleri = $this->db->get_where('reseller',array('id' =>$decideid))->row()->username;
	
	
	$msg= 'Change '.ucfirst($chnagetype).' Reseller: '.$reselleri.' New '.ucfirst($chnagetype).' '.$var['change'];
	$this->mit->InsertActivetUser($uid,$msg);
	

	print("<script>window.alert('Successfully change Sucure Update');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");

	}else {

		print("<script>window.alert('Admin PIN Wrong');</script>");
		print("<script>
	   	window.opener.location.reload();
	    window.close();
		</script>");

	}


	//redirect('portal/member', 'refresh');
	}else {
	$data['memedit']=$this->mdb->getData('reseller',array('id'=>$decideid));
	
	$this->load->view('admin/status/change_pass', $data);
	}



	
	}

	public function reseller_edit($uid){

		$decideid = $this->mdb->passwordChanger('decrypt', $uid); 
		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
		$sucdate=date('Y-m-d H:i:s'); 
		$idate=date('Y-m-d'); 
		$var=$this->input->post();	
		$varget=$this->input->get();

		$username = $this->db->get_where('reseller',array('id' =>$decideid))->row()->username;
		$ctype = $this->db->get_where('reseller',array('id' =>$decideid))->row()->custype;

		if($_POST) {
		$uname=$var['username'];
		$name=$var['name'];
		$mobile=$var['mobile'];
		$balance_limit=$var['balance_limit'];
		$active=$var['active'];
		$oversale=$var['oversale'];
		$api=$var['api'];
		$level=$var['level'];
			$fb_p=$var['fb'];
		$email=$var['email'];
		$dlock=$var['dlock'];
	$nid=$var['nid'];
            $birth=$var['birth'];
$recharge=$var['recharge'];
$new_account_price=$var['new_account_price'];
		$pin = $var['pincode'];

		$weballow       = $var['webaccess'];
		$appsallow       = $var['appsaccess'];
$ress       = $var['reseller'];

		$typ = 'admin';

		$otpchking = $this->mit->otpchk($pin,$typ);

		if($otpchking) {

		$per = 0; 
		
		if (!empty($var['client_typess'])): 
	    foreach ($var['client_typess'] as $per): 
	        $add_type_values+= $per; 
	    endforeach; 
		endif; 
		
		$reselleri = $this->db->get_where('reseller',array('id' =>$decideid))->row()->username;


		$query="UPDATE `reseller` SET `type` = '$add_type_values',
		`appsAccess` = '$appsallow',
		`webAccess` = '$weballow', 
		`reseller` = '$ress',
		`name` = '$name',
		`username` = '$uname',
		`nid` = '$nid',
          `birth` = '$birth',
		`mobile` = '$mobile',
		`email` = '$email', 
		`custype` = '$level',
			`recharge` = '$recharge', 
			`fb_p` = '$fb_p', 
		`balance_limit` = '$balance_limit', 
		`oversale` = '$oversale', 
		`status` = '$active', 
		`device_lock`='$dlock',
		`api` = '$api',`new_account_price`='$new_account_price' WHERE `reseller`.`id` ='$decideid'";

		$query2 = $this->db->query($query);
		
		if($var['otp']==0){
		    
		    $odata=array(
			                	'otp_choice' =>'0',
			                	'enbale_otp' =>0, 
			  );
			  
			  	$this->db->where('id',$decideid);
		$this->db->update('reseller',$odata);
		    
		}
		
		
		
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		
		$msg= 'Edit Reseller: '.$reselleri.' Balance Limit '.$balance_limit.' Status '.$active.' versale '.$oversale. ' Api '.$api.' Mobile '.$mobile.' Email '.$email;
		$this->mit->InsertActivetUser($uid,$msg);
		$mylevel=substr($level, -1);
		//print_r($add_type_values);
	redirect('admin/resellers/'.$mylevel, 'refresh');

		}else {

		$this->session->set_flashdata('error', 'Admin PIN Wrong');

		redirect('admin/resellers/'.$level, 'refresh');
		}

		}

			

		$query="select * from reseller where id='$decideid'"; 

		$query2 = $this->db->query($query);
		$data['edit_reseller'] = $query2->result();	

		$data['page_title'] = $username.' Edit Reseller';

		$data['page_name'] = 'reseller_edit';
		$this->load->view('admin/index', $data);

	}



	public function big_edit($para1="")
	{
if ($_POST) {
		$var=$this->input->post();				
		if(empty($var['status'])){ $staus='0'; }else{$staus='1';}
		$addofer = array(
					'pk_name' =>$var['pk_name'],
					'price' =>$var['price'],
					'comm' =>$var['comm'],
					'charge' =>0,
					'exp' =>$var['exp'],
          'status' =>$staus,
		
				);

		$this->db->where('id',$para1);
		$this->db->update('drive_package',$addofer);
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Edit Offer By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		$op = $this->db->get_where('drive_package',array('id' =>$para1))->row()->op;
		redirect("admin/big_pack/$op", 'refresh');

		$this->session->set_flashdata('success', 'Package Update Successfully');	


		}
  
  	$data['eid'] = $para1;
		$data['page_title'] = 'Edit Drive pkg';
		$data['page_name'] = 'drive_edit';

	$this->load->view('admin/index', $data);
  
  
}
  
	public function big_pack($para1="", $para2="", $para3="")
	{
		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
		$sucdate=date('Y-m-d H:i:s'); 
		$idate=date('Y-m-d'); 
		$var=$this->input->post();	
		$varget=$this->input->get();

	 

		if(!empty($_POST['todo'])){

		$this->form_validation->set_rules('pk_name', 'Package Name', 'is_unique[drive_package.pk_name]|trim|required|xss_clean|min_length[3]');
		
		$this->form_validation->set_rules('price', 'Amount Not Work', 'numeric|greater_than[0.99]|required|xss_clean');

		if ($this->form_validation->run() == FALSE) {
			$data['page_title'] = 'Manage Offer Package ';

		$data['page_name'] = 'add_drive_oparetor_pack';
		}else {
		    
		    	$firstThreeDigit = $this->db->get_where('net_op',array('id' =>$var['op']))->row()->prefix;
		    		$opname = $this->db->get_where('net_op',array('id' =>$var['op']))->row()->opname;
		    	
		    if($firstThreeDigit=="017"){ 
		$operator="GP";
		}if($firstThreeDigit=="013"){ 
		$operator="GP";
		}if($firstThreeDigit=="019"){ 
		$operator="BL";
		}if($firstThreeDigit=="014"){ 
		$operator="BL";
		}if($firstThreeDigit=="018"){ 
		$operator="RB"; 
		}if($firstThreeDigit=="016"){ 
		$operator="AT"; 
		}if($firstThreeDigit=="015"){ 
		$operator="TT"; 
		}
		

				$addofer = array(
					'pk_name' =>$var['pk_name'],
					'op' =>$var['op'],
					'price' =>$var['price'],
					'comm' =>$var['comm'],
					'charge' =>0,
					'exp' =>$var['exp'],
					'status' =>1,
					'volume'=>$operator,
					'date' =>$sucdate
				);

			$this->db->insert('drive_package',$addofer);
			 $this->mit->send_fcm_msg("$opname New Offer",$var['pk_name'],"","1235","all"); 
			$user=$this->session->userdata('admin_session');
			$uid= $user->id;
			$msg= 'Add Offer By Admin';
			$this->mit->InsertActivetUser($uid,$msg);

			$this->session->set_flashdata('success', 'Package Added Successfully');

			redirect("admin/big_pack/$para1", 'refresh');
			}
		}else if(!empty($_POST['do'])){
		     if($var['status']==3){
                 $cnt=count($var['id']); 
               for($i=0;$i<$cnt;$i++) 
		{ $del_id=$var['id'][$i]; 
		
	
		$queryrate="DELETE from `drive_package` where id='$del_id'"; 

		$this->db->query($queryrate);

		}
               
               
             }
		    
		    if($var['status']!=3){
		        
		        
		        $cnt=count($var['id']); 
	if($var['status']==1)$status=1;
              if($var['status']==0)$status=0;
       
		for($i=0;$i<$cnt;$i++) 
		{ $del_id=$var['id'][$i]; 
		
	
		$queryrate="UPDATE `drive_package` SET `status` = '$status' where id='$del_id'"; 

		$this->db->query($queryrate);

		}
		        
		        
		    }
		    
		  	redirect("admin/big_pack/$para1", 'refresh');  
		}

		$data['page_title'] = 'Manage Drive Package ';

		$data['page_name'] = 'add_drive_oparetor_pack';
			
		

		
		$data['eid'] = $para1;
		$this->load->view('admin/index', $data);
		
	}
	
	
public function offer_edit($para1="")
	{
if ($_POST) {
		$var=$this->input->post();				
		if(empty($var['status'])){ $staus='0'; }else{$staus='1';}
		$addofer = array(
					'pk_name' =>$var['pk_name'],
					'price' =>$var['price'],
					'comm' =>$var['comm'],
					'charge' =>0,
					'exp' =>$var['exp'],
          'status' =>$staus,
		
				);

		$this->db->where('id',$para1);
		$this->db->update('net_package',$addofer);
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Edit Offer By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		$op = $this->db->get_where('net_package',array('id' =>$para1))->row()->op;
		redirect("admin/offer/$op", 'refresh');

		$this->session->set_flashdata('success', 'Package Update Successfully');	


		}
  
  	$data['eid'] = $para1;
		$data['page_title'] = 'Edit Drive pkg';
		$data['page_name'] = 'offer_edit';

	$this->load->view('admin/index', $data);
  
  
}	
	
   public function b_operator()
	{
		$data['page_title'] = 'Operator';

		$data['page_name'] = 'drive_operator';
		$this->load->view('admin/index', $data);
	}
	
	
	 public function d_operator()
	{
		$data['page_title'] = 'Operator';

		$data['page_name'] = 'offer_operator';
		$this->load->view('admin/index', $data);
	}


	public function offer($para1="", $para2="", $para3="")
	{
		$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
		$sucdate=date('Y-m-d H:i:s'); 
		$idate=date('Y-m-d'); 
		$var=$this->input->post();	
		$varget=$this->input->get();

	 

		if(!empty($_POST['todo'])){

		$this->form_validation->set_rules('pk_name', 'Package Name', 'is_unique[drive_package.pk_name]|trim|required|xss_clean|min_length[3]');
		
		$this->form_validation->set_rules('price', 'Amount Not Work', 'numeric|greater_than[0.99]|required|xss_clean');

		if ($this->form_validation->run() == FALSE) {
			$data['page_title'] = 'Manage Offer Package ';

		$data['page_name'] = 'add_net_oparetor_pack';
		}else {
		    
		    	$firstThreeDigit = $this->db->get_where('net_op',array('id' =>$var['op']))->row()->prefix;
		    	$opname = $this->db->get_where('net_op',array('id' =>$var['op']))->row()->opname;
		    if($firstThreeDigit=="017"){ 
		$operator="GP";
		}if($firstThreeDigit=="013"){ 
		$operator="GP";
		}if($firstThreeDigit=="019"){ 
		$operator="BL";
		}if($firstThreeDigit=="014"){ 
		$operator="BL";
		}if($firstThreeDigit=="018"){ 
		$operator="RB"; 
		}if($firstThreeDigit=="016"){ 
		$operator="AT"; 
		}if($firstThreeDigit=="015"){ 
		$operator="TT"; 
		}
		

				$addofer = array(
					'pk_name' =>$var['pk_name'],
					'op' =>$var['op'],
					'price' =>$var['price'],
					'comm' =>$var['comm'],
					'charge' =>0,
					'status' =>1,
					'volume'=>$operator,
					'date' =>$sucdate
				);

			$this->db->insert('net_package',$addofer);
			 $this->mit->send_fcm_msg("$opname regular new offer",$var['pk_name'],"","1235","all"); 
			$user=$this->session->userdata('admin_session');
			$uid= $user->id;
			$msg= 'Add Offer By Admin';
			$this->mit->InsertActivetUser($uid,$msg);

			$this->session->set_flashdata('success', 'Package Added Successfully');

			redirect("admin/offer/$para1", 'refresh');
			}
		}else if(!empty($_POST['do'])){
		     if($var['status']==3){
                 $cnt=count($var['id']); 
               for($i=0;$i<$cnt;$i++) 
		{ $del_id=$var['id'][$i]; 
		
	
		$queryrate="DELETE from `net_package` where id='$del_id'"; 

		$this->db->query($queryrate);

		}
               
               
             }
		    
		    if($var['status']!=3){
		        
		        
		        $cnt=count($var['id']); 
	if($var['status']==1)$status=1;
              if($var['status']==0)$status=0;
       
		for($i=0;$i<$cnt;$i++) 
		{ $del_id=$var['id'][$i]; 
		
	
		$queryrate="UPDATE `net_package` SET `status` = '$status' where id='$del_id'"; 

		$this->db->query($queryrate);

		}
		        
		        
		    }
		    
		  	redirect("admin/offer/$para1", 'refresh');  
		}

		$data['page_title'] = 'Manage Reguler Package ';

		$data['page_name'] = 'add_net_oparetor_pack';
			
		

		
		$data['eid'] = $para1;
		$this->load->view('admin/index', $data);
		

	    
	    
	    
	}

	public function resellers($level="", $page="", $username="", $status="", $limit="") {


	$var=$this->input->post();
	$varget=$this->input->get();

	$action = isset($var["action"]) ? $var["action"] : null;

	$cnt = isset($var['id']) && is_array($var['id']) ? count($var['id']) : 0;

	if(isset($action)) {
		if($action=='inactive') {$stsac='0';}
		if($action=='delete') {$stsac='2';}
		if($action=='active') {$stsac='1';}
		//if($action=='otpdisable') {$otpdisable='0';}

		for($i=0;$i<$cnt;$i++) { 
		 $del_id=$var['id'][$i]; 
		 
		 $reselleri = $this->db->get_where('reseller',array('id' =>$del_id))->row()->username;
		 
		 if($action=='otp') {
			 $action='OTP Disable';
			 
		 $query="UPDATE  `reseller` SET  `enbale_otp` =  '0',`otp_choice` =  '0' where id='$del_id'"; 
		 $this->db->query($query);
		 }else {
			 
		$query="UPDATE  `reseller` SET  `status` =  '$stsac', `delete` =  '$stsac' where id='$del_id'"; 
		 $this->db->query($query);
			 
		 }
			
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= ucfirst($action). ' Reseller: '.$reselleri;
		$this->mit->InsertActivetUser($uid,$msg);

		}
		print("<script>
	   	window.opener.location.reload();
	   
		</script>");
	}	

	if($_POST){
	$username=$var["cname"]; 
	$resel=$var["resel"]; 
	$status=$var["status"];
	$limit=$var["limit"]; 
	
	}else {

	$username=$varget["cname"]; 
	$status=$varget["status"]; 
	$limit=$varget["limit"];
	$resel=$varget["resel"]; 
	$restype=$varget["restype"];

	}

	if($level=="subadmin") {
		$restype='subadmin';
	}else {
		$restype='reseller'.$level;
	}

	$data['level']= $level;
	

	if(empty($limit) ) { 
		 $limit = 25; 
		 }

	$link="restype=$restype&cname=$username&resel=$resel&status=$status&limit=$limit"; 

	$targetpage = "?"; 
	 //your file name  (the name of this file) 
	$pageselect=$varget['pageid']; 
		 //how many items to show per page 
	$page = $varget['page']; 
	if($page)  
	$start = ($page - 1) * $limit;       //first item to display on this page 
	else 
	$start = 0;                //if no page var is given, set start to 0 
		

	// only user
	if($username!="" && $status=="all" && $resel=="all" &&  $restype!="resellerall") 
  	{ 
	$sql="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin' and custype='$restype' and status!=2 order by id desc LIMIT $start,$limit";

	$sqlcont="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin' and custype='$restype' and status!=2 order by id desc"; 

	}

	elseif($username!="" && $status=="all" && $resel=="all" &&  $restype=="resellerall") 
  	{ 
	$sql="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin'  and status!=2 order by id desc LIMIT $start,$limit";

	$sqlcont="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin' and status!=2 order by id desc"; 

	}
	//only user finish ///

	//all level
	elseif($username=="" && $status=="all" && $resel=="all" && $restype=="resellerall") 
  	{ 
	$sql="SELECT * from reseller where custype!='admin' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where custype!='admin' and status!=2 order by id desc"; 
   
	}


	// level 
	elseif($username=="" && $status=="all" && $resel=="all" && $restype!="resellerall") 
  	{ 
	$sql="SELECT * from reseller where custype!='admin' and custype='$restype' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where custype!='admin' and custype='$restype' and status!=2 order by id desc"; 
   
	}

	/// level select

	elseif($status=="all" && $username!="" && $resel!="all" && $restype!="resellerall") 
  	{ 
	$sql="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin' and custype='$restype' and p_id='$resel' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin' and custype='$restype' and p_id='$resel' and status!=2 order by id desc"; 
   
	}


	elseif($status=="all" && $username!="" && $resel!="all" && $restype=="resellerall") 
  	{ 
	$sql="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin' and p_id='$resel' and  status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where `username` LIKE '%$username%' and custype!='admin' and p_id='$resel' and status!=2 order by id desc"; 
   
	}

	elseif($status!="all" && $username=="" && $resel=="all" && $restype!="resellerall") 
  	{ 
	$sql="SELECT * from reseller where status='$status' and custype!='admin' and custype='$restype' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where status='$status' and custype!='admin' and custype='$restype' and status!=2 order by id desc"; 
   
	}


	elseif($status!="all" && $username=="" && $resel=="all" && $restype=="resellerall") 
  	{ 
	$sql="SELECT * from reseller where status='$status' and custype!='admin' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where status='$status' and custype!='admin' and status!=2 order by id desc"; 
   
	}

	elseif($username!="" && $status!="all" && $resel=="all" && $restype!="resellerall") 
  	{ 
	$sql="SELECT * from reseller where `username` LIKE '%$username%' and status='$status' and custype!='admin' and custype='$restype' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where `username` LIKE '%$username%' and status='$status' and custype!='admin' and custype='$restype' and status!=2 order by id desc"; 
   
	}

	elseif($username!="" && $status!="all" && $resel!="all" && $restype!="resellerall") 
  	{ 
	$sql="SELECT * from reseller where `username` LIKE '%$username%' and p_id='$resel' and status='$status' and custype!='admin' and custype='$restype' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where `username` LIKE '%$username%' and p_id='$resel' and status='$status' and custype!='admin' and custype='$restype' and status!=2 order by id desc"; 
   
	}

	

	elseif($username!="" && $status!="all" && $resel!="all" && $restype=="resellerall") 
  	{ 
	$sql="SELECT * from reseller where `username` LIKE '%$username%' and p_id='$resel' and status='$status' and custype!='admin' and status!=2 order by id desc LIMIT $start,$limit"; 

	$sqlcont="SELECT * from reseller where `username` LIKE '%$username%' and p_id='$resel' and status='$status' and custype!='admin' and status!=2 order by id desc"; 
   
	}

	

	else {
$idc = explode(".", $level);
		if($level=="all"){

$sql="SELECT * from reseller where custype!='admin' and status!=2 order by id desc LIMIT $start,$limit";

	$sqlcont="SELECT * from reseller where custype!='admin' and status!=2 order by id desc"; 


	}else {
if($idc[0]==9){

$sql="SELECT * from reseller where custype!='admin' and status!=2 and p_id='$idc[1]' order by id desc LIMIT $start,$limit";

	$sqlcont="SELECT * from reseller where custype!='admin' and status!=2 and p_id='$idc[1]' order by id desc"; 


	}else{
	$sql="SELECT * from reseller where custype!='admin' and custype='$restype'  and custype='$restype' and status!=2 order by id desc LIMIT $start,$limit";

	$sqlcont="SELECT * from reseller where custype!='admin' and custype='$restype' and status!=2 order by id desc"; 
	}
		}
	
	}

	

	$querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
		  } 

	$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);
	$sql9="SELECT * from reseller where custype!='admin' and status!=2 order by id desc";
	$query9 = $this->db->query($sql9);

	$query2 = $this->db->query($sql);

	$data['limit'] = $limit;
	$data['cname'] = $username;
	$data['status'] = $status;
	$data['resel'] = $resel;
	$data['restype'] = $restype;

	$data['links'] = $pagination;
	$data['reseller_all'] = $query2->result();
		$data['reseller_bl'] = $query9->result();
	$data['page_title'] = ' Reseller '. $level;

	$data['page_name'] = 'reseller_list';
	$this->load->view('admin/index', $data);

	}

	public function addreseller($level=""){

	$user=$this->session->userdata('admin_session');
	$uid= $user->id;
	$sendername = $user->username;
	$myLeft = $user->lft;
	$myRight = $user->rgt;

	$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A'); 
		$ip = $_SERVER['REMOTE_ADDR']; 		

	if($_POST){

	$this->form_validation->set_rules('username', 'Username', 'is_unique[reseller.username]|trim|required|xss_clean|min_length[5]');

	$this->form_validation->set_rules('password', 'Password', 'trim|required|xss_clean|min_length[6]');
	
	$this->form_validation->set_rules('pin', 'PIN', 'trim|required|xss_clean|min_length[4]|numeric');
	
	$this->form_validation->set_rules('pincode', 'Salf PIN', 'trim|required|xss_clean|min_length[4]');
	
	$this->form_validation->set_rules('per[]', 'Reseller Permission', 'trim|required');
		
	if ($this->form_validation->run() == FALSE) {
	$data['page_name'] = 'add_reseller';
	}else {
		
		

		if($level=="subadmin") {
			$custype='subadmin';
		}else if ($level=="all") {
			$custype='subadmin';
		}else {
			$custype='reseller'.$level;
		}
		

	$var=$this->input->post();
		
	$username  = $var['username'];
	$password  = $var['password'];
	$pin  	   = $var['pin'];
	$mobile    = $var['mobile'];
	$email     = $var['email'];
	$per       = $var['per'];
	$weballow       = $var['webaccess'];
	$appsallow       = $var['appsaccess'];
	$apiset       = $var['apiset'];
	$oversale       = $var['oversale'];
	$active =    $var['active'];
	$pincode =    $var['pincode'];
	
	$typ = 'admin';

	$otpchking = $this->mit->otpchk($pincode,$typ);

	if($otpchking) {
	
	if($active==1) {$active="1";}else {$active="0";}
	if($oversale==1) {$oversale="1";}else {$oversale="0";}
	if($apiset==1) {$apiset="1";}else {$apiset="0";}
	

	$per = 0; 
		
	if (!empty($var['per'])): 
    foreach ($var['per'] as $per): 
        $add_type_values+= $per; 
    endforeach; 
	endif; 

	
		
		$userpl=strlen($username); 
		$pass=strlen($password);

		$q2="UPDATE reseller SET rgt = rgt + 2 WHERE rgt > $myLeft"; 
		$this->db->query($q2);	

		$q3="UPDATE reseller SET lft = lft + 2 WHERE lft > $myLeft"; 
		$this->db->query($q3);


		$left=$myLeft +1; 
		$right=$myLeft + 2; 
		$hash = $this->mdb->generate($password); 
		$hashpin = $this->mdb->generate($pin);
		
		$rlastids = $this->mit->lastUserID();
		 $rlastid = $rlastids+1;

		$sqltarif="INSERT INTO `tarif` (`id`, `make_by`, `name`, `desc`, `userid`, `duble`, `date`, `time`) VALUES (NULL, '$uid', '$username Tarif', '$add_type_values', '$rlastid', '$duble', '$idate', '$create_date');"; 
		
		$this->db->query($sqltarif);
		
	

		$tarifidlast = $this->db->insert_id();

		

		$reselleradd = array(
					'username' =>$username,
					'password' =>$hash,
					'pincode' =>$hashpin,
					'lft' =>$left,
					'rgt' =>$right,
					'type' =>$add_type_values,
					'p_id' =>$uid,
					'mobile' =>$mobile,
					'custype' =>$custype,
					'tarif' =>$tarifidlast,
					'webAccess' =>$weballow,
					'appsAccess' =>$appsallow,
					'enbale_otp' =>0,
					'balance' =>0,
					'api' =>$apiset,
					'oversale' =>$oversale,
					'status' =>$active,
					'create_date' =>$create_date
					);

		$this->mdb->insert('reseller',$reselleradd);

		$idlast = $this->db->insert_id();

		 $company = $this->db->get_where('company',array('id' =>'1'))->row()->company_name;
 
      
			$msg="Dear your account has been created sucessfully on $company username: $username Pin: $pin password: $password";
			if(is_numeric($mobile)){
			
		$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($mobile,$msg); 
		     }else{	

			    $this->mdb->sms_send($mobile,$msg);
		     }
            }
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add Reseller By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		redirect('admin/resellers/'.$level, 'refresh');
		
		}else {
	$this->session->set_flashdata('error', 'PIN code Wrong');
	$data['page_name'] = 'add_reseller';
	}

		}
	

		

	}else{

	$data['page_title'] = ' Reseller '. $level;

	$data['page_name'] = 'add_reseller';
	}
	$this->load->view('admin/index', $data);
	}

	public function country($para1="", $para2="", $para3="")
	{

	$get=$this->input->get();
	$var=$this->input->post();
	$create_date = $this->date_time();

	if($para1=="add"){

	if($_POST){
	$country_name=$var['country_name']; 
	$code=$var['code']; 
	$currency=$var['currency']; 
	$phone=$var['phone']; 
	$status=$var['status']; 

	$sql="INSERT INTO `country` (`id`, `country_name`, `code`, `currency`, `phone`, `status`, `date`) VALUES (NULL, '$country_name', '$code', '$currency', '$phone', '$status', '$create_date')"; 

	$this->db->query($sql);
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add Country By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
	redirect('admin/country', 'refresh');
	}

	$data['page_title'] = ' Country Add ';
	$data['page_name'] = 'country/country_add';
	}else {

	if($_POST){
	$country=$var['country']; 
	$sts=$var['sts']; 
	$data['country'] = $country;
	$data['sts'] = $sts;	
	}
	$data['page_title'] = ' Country List ';
	$data['page_name'] = 'country/country';

	}
	$this->load->view('admin/index', $data);
	}


	public function addussd()
	{

	$create_date = $this->date_time();

	if($_POST){

	$var=$this->input->post();
	$name = $var['name'];
	$ussd = $var['ussd'];
	$id_modem = $var['modem'];
	$amount = $var['amount'];
	$auto = $var['auto'];
	$status = $var['status'];
$triger = $var['triger'];

	$a = $var['a'];
	$b = $var['b'];
	$c = $var['c'];
	$d = $var['d'];
	$e = $var['e'];
	$offer = $var['offer'];

		$sql="INSERT INTO `siminfo` (`id`, `names`, `amount`, `ussd`, `status`, `auto`, `a`, `b`, `c`, `d`, `e`, `date`,`id_modem`,`triger`,`offer`) VALUES (NULL, '$name', '$amount', '$ussd', '$status', '$auto', '$a', '$b', '$c', '$d', '$e', '$create_date','$id_modem','$triger','$offer')";
	$this->db->query($sql);
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add modem Rate By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
	redirect('admin/modemlist/', 'refresh');	 	

	}else {

		$data['page_title'] = 'Add ussd ';
		$data['page_name'] = 'add_modem_rate';
	}

		
		$this->load->view('admin/index', $data);
	}







	public function oparetor($para1="", $para2="", $para3="")
	{

	$get=$this->input->get();
	$var=$this->input->post();
	$create_date = $this->date_time();

	if($para1=="add"){
		if($_POST){

	$country=$var['country']; 
	$coutryPhone=$var['coutryPhone']; 
	$code=$var['code']; 
	$title=$var['title']; 
	$oprefix=$var['oprefix']; 
	$length=$var['length']; 
	$status=$var['status']; 

	$sql="INSERT INTO `country_op` (`id`, `country_id`, `country_code`, `op_name`, `code`, `oprefix`, `lenth`, `status`, `date`) VALUES (NULL, '$country', '$coutryPhone', '$title', '$code', '$oprefix',  '$length', '$status', '$create_date')";
	$this->db->query($sql);
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add Country Oparetor By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

	redirect('admin/oparetor', 'refresh');

	}
	$data['page_title'] = ' Oparetor Add ';
	$data['page_name'] = 'country/operators_add';
	}else if($para1=="edit"){
	
	$data['oid'] = $para2;
	$data['page_title'] = ' Oparetor Edit ';
	$data['page_name'] = 'country/operators_edit';
		
	}else if($para1=="update") {
		
		
	$country=$var['country']; 
	$coutryPhone=$var['coutryPhone']; 
	$code=$var['code']; 
	$title=$var['title']; 
	$oprefix=$var['oprefix']; 
	$length=$var['length']; 
	$status=$var['status']; 
	
		$sql="UPDATE  `country_op` SET  `country_id` =  '$country', 
		`country_code` =  '$coutryPhone', 
		`op_name` =  '$title', 
		`code` =  '$code', 
		`oprefix` =  '$oprefix', 
		`lenth` =  '$length', 
		`status` =  '$status' WHERE  `country_op`.`id` ='$para2'; 
		"; 
		$this->db->query($sql);
		
		redirect('admin/oparetor', 'refresh');

	}
	else if($para1=="change") {

			if($para2=="active") {
			$change = array('status' =>0);
			$stschnge = "Deactive";
			}else {
			$change = array('status' =>1);
			$stschnge = "Active";
			}

		$this->mdb->update('country_op',$change,array('id'=>$para3));
			
		
		

        redirect('admin/oparetor/', 'refresh');	

		}
	else {

	
	if($_POST){

	$opnam = $var["opnam"]; 
	$countrynam = $var["countrynam"];
	$data['opnam'] = $opnam;
	$data['countrynam'] = $countrynam;	
	} 

	$data['page_title'] = ' Oparetor List ';
	$data['page_name'] = 'country/operators_list';


	}


	$this->load->view('admin/index', $data);
	}





	public function ussd($para1="", $para2="") {


	if($para1=="update"){

		if($_POST){

		$var=$this->input->post();
		$name=$var['names']; 
		$ussd=$var['ussd']; 
		$amount=$var['amount'];
        $auto=$var['auto']; 
		$st=$var['status'];
			
		$a=$var['a']; 
		$b=$var['b']; 
		$c=$var['c']; 
		$d=$var['d']; 
		$e=$var['e']; 
			$offer=$var['offer']; 
		$cnt=count($var['chkbox']); 
		$triger=$var['triger'];
          $powerload=$var['powerload'];
		for($i=0;$i<$cnt;$i++) 
		{ 
		$del_id=$var['chkbox'];

		$queryrate="UPDATE  `siminfo` SET `names` =  '$name[$i]', `status` =  '$st[$i]',`ussd` =  '$ussd[$i]',`amount` =  '$amount[$i]',`a` =  '$a[$i]',`b` =  '$b[$i]',`c` =  '$c[$i]',`d` =  '$d[$i]',`e` = '$e[$i]',`auto` =  '$auto[$i]',`triger` = '$triger[$i]',`offer` = '$offer[$i]',`powerload` = '$powerload[$i]' where id='$del_id[$i]'"; 

		$this->db->query($queryrate);
$id_modem =$this->db->get_where('siminfo',array('id' =>$del_id[$i]))->row()->id_modem;
		}
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Rate Modual Update Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		redirect("admin/ussd/$id_modem", 'refresh');
	}

	}else if ($para1=="delete") {
		$id_modem =$this->db->get_where('siminfo',array('id' =>$para2))->row()->id_modem;	
	$this->db->where("id",$para2);
    $this->db->delete("siminfo");
    
    
    
	redirect("admin/ussd/$id_modem", 'refresh');
		$this->session->set_flashdata('success', ' Delete Successfully');

		}else if ($para1=="reset") {
		    $this->db->query("TRUNCATE `siminfo`");
		   $sql = file_get_contents('http://ictfairs.flexisoftwarebd.com/ussd.sql');  
		   
		   $this->db->query($sql);
		   
		   redirect("admin/modemlist", 'refresh');
		   
		}else {

	$sql="select * from siminfo where id_modem='$para1' order by id desc"; 
		 
	$query11 = $this->db->query($sql);		
	$reselerrate = $query11->result() ;
		 $querycount = $this->db->query($sqlcont);
		  
		  $total_item = $query11->num_rows();
		  
	$data["all_rate"] = $reselerrate;
$data["item_count"] = $total_item;

	$data['page_title'] = ' USSD Dial ';
	$data['page_name'] = 'modem_rate';
	}
	$this->load->view('admin/index', $data);

	}







	public function comm($uid="")
	{
		$get=$this->input->get();
		$var=$this->input->post();
		if($this->input->post() && !empty($cname))
		 {}else {
			 
		$residhex=$this->mdb->passwordChanger('decrypt', $uid); 
		
		$tarif = $this->db->get_where('reseller',array('id' =>$residhex))->row()->tarif;

		$username = $this->db->get_where('reseller',array('id' =>$residhex))->row()->username;		
			 
		 $prefix=$var['prefix']; 
             
         $opname=$var['opname']; 
             

		if($prefix!="" && $opname==""){ 
		$sql="select * from rate_com where `prefix` = '$prefix' and tarif='$tarif'"; 
		} else if($opname!="" && $prefix==""){ 
		$sql="select * from rate_com where `service` = '$opname' and tarif='$tarif'"; 

		} else if($opname!="" && $prefix!=""){ 
		$sql="select * from rate_com where service = '$opname' and prefix = '$prefix' and tarif='$tarif'"; 
		}else { 
		$sql="select * from rate_com where  tarif='$tarif' order by id asc"; 
		} 

			 
		 
		$query11 = $this->db->query($sql);		
		$reselerrate = $query11->result() ;
		$data["tarifid"] = $tarif;	 
		$data["reseller_rate"] = $reselerrate;
		$data["links"] = $pagination;
		$data['prefix']=$prefix;    
        $data['opname']=$opname; 
		$data["username"] = $username;		
		$data['page_title'] = 'Reseller '. $username;
		$data['page_name'] = 'comm';
		$this->load->view('admin/index', $data);
			 
		 }
	}



	public function rate($uid="")
	{
		$get=$this->input->get();
		$var=$this->input->post();
		if($this->input->post() && !empty($cname))
		 {}else {
			 
		$residhex=$this->mdb->passwordChanger('decrypt', $uid); 
		
		$tarif = $this->db->get_where('reseller',array('id' =>$residhex))->row()->tarif;

		$username = $this->db->get_where('reseller',array('id' =>$residhex))->row()->username;		
			 
		 $prefix=$var['prefix']; 
             
         $opname=$var['opname']; 
             

		if($prefix!="" && $opname==""){ 
		$sql="select * from rate_module where `prefix` = '$prefix' and tarif='$tarif'"; 
		} else if($opname!="" && $prefix==""){ 
		$sql="select * from rate_module where `service` = '$opname' and tarif='$tarif'"; 

		} else if($opname!="" && $prefix!=""){ 
		$sql="select * from rate_module where service = '$opname' and prefix = '$prefix' and tarif='$tarif'"; 
		}else { 
		$sql="select * from rate_module where  tarif='$tarif' order by id asc"; 
		} 

			 
		 
		$query11 = $this->db->query($sql);		
		$reselerrate = $query11->result() ;
		$data["tarifid"] = $tarif;	 
		$data["reseller_rate"] = $reselerrate;
		$data["links"] = $pagination;
		$data['prefix']=$prefix;    
        $data['opname']=$opname; 
		$data["username"] = $username;		
		$data['page_title'] = 'Reseller '. $username;
		$data['page_name'] = 'rate';
		$this->load->view('admin/index', $data);
			 
		 }
	}

	public function rate_code()
	{
		
		$var=$this->input->get();

		$country=$var['country']; 

		$data['id'] = $country;
		$this->load->view('admin/country/rate_code', $data);
	}

	public function getop_code()
	{
		
		$var=$this->input->get();

		$operator=$var['operator']; 

		$data['id'] = $operator;
		$this->load->view('admin/country/get_op_code', $data);
	}

	public function addrate()
	{

	$create_date = $this->date_time();

	if($_POST){

	$var=$this->input->post();
	$country = $var['country'];
	$country_code = $var['country_code'];
	$operator = $var['operator'];
	$opcode = $var['opcode'];
	$services = $var['services'];
	$buyrate = $var['buyrate'];

	$rate = $var['rate'];
	$commision = $var['commision'];
	$charge = $var['charge'];
	$type = $var['type'];
	$prefix = $var['prefix'];
	$code = $var['code'];


		$sql="INSERT INTO `price` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `service`, `buyrate`, `rate`, `comm`, `charge`, `type`, `pcode`, `prefix`, `date`) VALUES (NULL, '$country', '$country_code', '$operator', '$opcode', '$services', '$buyrate', '$rate', '$commision', '$charge', '$type', '$code', '$prefix', '$create_date')";
	$this->db->query($sql);
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add Rate By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
	redirect('admin/rateModual/', 'refresh');	 	

	}else {

		$data['page_title'] = 'Add Rate ';
		$data['page_name'] = 'addrate';
	}

		
		$this->load->view('admin/index', $data);
	}

	public function rateUpdate($uid="")
	{
		$var=$this->input->post();

		$userid=$var['userid']; 

		$tarpcode=$var['tarpcode']; 
		$edit_com=$var['edit_com']; 
		$edit_rate=$var['rate_edit']; 
        $edit_charge=$var['edit_charge']; 
		$ratestatus=$var['ratestatus'];
		$tarifid=$var['tarifid']; 
		$cnt=count($var['edit_com']); 
		
		for($i=0;$i<$cnt;$i++) 
		{ 
		$del_id=$var['chkbox'];

		$queryrate="UPDATE  `rate_module` SET `rate` =  '$edit_rate[$i]', `comm` =  '$edit_com[$i]', 
		`charge` =  '$edit_charge[$i]', `status` =  '$ratestatus[$i]' where id='$del_id[$i]'"; 

		$this->db->query($queryrate);
		
		
		
		}
		
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= 'Edit Rate Reseller: '.$userid;
		$this->mit->InsertActivetUser($uids,$msg);

		$ctype = $this->db->get_where('reseller',array('username' =>$userid))->row()->custype;
		
		if($ctype=="subadmin"){$level="subadmin";} 
		if($ctype=="reseller5"){$level="5";} 
		if($ctype=="reseller4"){$level="4";} 
		if($ctype=="reseller3"){$level="3";} 
		if($ctype=="reseller2"){$level="2";}
		if($ctype=="reseller1"){$level="1";}
	
		
		redirect('admin/resellers/'.$level, 'refresh');	 	 
  
  
	   
	}
	
	
	
	
	
	public function delcomm($para1="") {


	$this->db->where("id",$para1);
    $this->db->delete("com_price");
	
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Comm Modual Delete Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		redirect('admin/commModual', 'refresh');
	

	

	$data['page_title'] = ' Rate Modual ';
	$data['page_name'] = 'admin_comm';
	
	$this->load->view('admin/index', $data);

	}


	public function commModual($para1="", $para2="") {


	if($para1=="update"){

		if($_POST){

		$var=$this->input->post();
		$tarpcode=$var['tarpcode']; 
		$edit_com=$var['edit_com']; 
		$edit_price=$var['edit_price'];
		$edit_rate=$var['edit_rate'];
        $edit_charge=$var['edit_charge']; 
		$tarifid=$var['tarifid']; 
		$cnt=count($var['chkbox']); 
		
		for($i=0;$i<$cnt;$i++) 
		{ 
		$del_id=$var['chkbox'];

		$queryrate="UPDATE  `com_price` SET `rate` =  '$edit_rate[$i]', `comm` =  '$edit_com[$i]', `price` =  '$edit_price[$i]', 
		`charge` =  '$edit_charge[$i]' where id='$del_id[$i]'"; 

		$this->db->query($queryrate);
		}
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Rate Modual Update Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		redirect('admin/commModual', 'refresh');
	}

	}else {

	$sql="select * from com_price order by id asc"; 
		 
	$query11 = $this->db->query($sql);		
	$reselerrate = $query11->result() ;
		
	$data["all_rate"] = $reselerrate;

	$data['page_title'] = ' Rate Modual ';
	$data['page_name'] = 'admin_comm';
	}
	$this->load->view('admin/index', $data);

	}

	
	
	
		public function addcomm()
	{

	$create_date = $this->date_time();

	if($_POST){

	$var=$this->input->post();
	$country = $var['country'];
	$country_code = $var['country_code'];
	$operator = $var['operator'];
	$opcode = $var['opcode'];
	$services = $var['services'];
	$buyrate = $var['buyrate'];
	$price = $var['price'];
	$rate = $var['rate'];
	$commision = $var['commision'];
	$charge = $var['charge'];
	$type = $var['type'];
	$prefix = $var['prefix'];
	$code = $var['code'];


		$sql="INSERT INTO `com_price` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `service`, `buyrate`, `rate`, `comm`, `charge`, `type`, `pcode`, `prefix`, `date`,`price`) VALUES (NULL, '$country', '$country_code', '$operator', '$opcode', '$services', '$buyrate', '$rate', '$commision', '$charge', '$type', '$code', '$prefix', '$create_date','$price')";
	$this->db->query($sql);
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add Rate By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
	redirect('admin/commModual/', 'refresh');	 	

	}else {

		$data['page_title'] = 'Add Commision ';
		$data['page_name'] = 'addcomm';
	}

		
		$this->load->view('admin/index', $data);
	}

	
	
	
	
	
	
	
		public function commUpdate($uid="")
	{
		$var=$this->input->post();

		$userid=$var['userid']; 

		$tarpcode=$var['tarpcode']; 
		$edit_com=$var['edit_com'];
			$edit_price=$var['edit_price']; 
		$edit_rate=$var['rate_edit']; 
        $edit_charge=$var['edit_charge']; 
		$ratestatus=$var['ratestatus'];
		$tarifid=$var['tarifid']; 
		$cnt=count($var['edit_com']); 
		
		for($i=0;$i<$cnt;$i++) 
		{ 
		$del_id=$var['chkbox'];

		$queryrate="UPDATE  `rate_com` SET `rate` =  '$edit_rate[$i]', `comm` =  '$edit_com[$i]',  `price` =  '$edit_price[$i]', 
		`charge` =  '$edit_charge[$i]', `status` =  '$ratestatus[$i]' where id='$del_id[$i]'"; 

		$this->db->query($queryrate);
		
		
		
		}
		
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= 'Edit Rate Reseller: '.$userid;
		$this->mit->InsertActivetUser($uids,$msg);

		$ctype = $this->db->get_where('reseller',array('username' =>$userid))->row()->custype;
		
		if($ctype=="subadmin"){$level="subadmin";} 
		if($ctype=="reseller5"){$level="5";} 
		if($ctype=="reseller4"){$level="4";} 
		if($ctype=="reseller3"){$level="3";} 
		if($ctype=="reseller2"){$level="2";}
		if($ctype=="reseller1"){$level="1";}
	
		
		redirect('admin/resellers/'.$level, 'refresh');	 	 
  
  
	   
	}
	
	
	
		public function commSync($uid="") {
		
		$create_date = date('Y-m-d H:i:s');
		
		$member = $this->session->userdata('admin_session');
		
		$sessuid = $member->id;
		
		$residhex=$this->mdb->passwordChanger('decrypt', $uid); 
		
$trif_id = $this->db->get_where('reseller',array('username' =>$residhex))->row()->tarif;

$userid = $this->db->get_where('reseller',array('username' =>$residhex))->row()->id;

$ctype = $this->db->get_where('reseller',array('username' =>$residhex))->row()->custype;


		
		$sql353="SELECT * from com_price"; 
 
		$query2 = $this->db->query($sql353);
		foreach ($query2->result() as $rowprice)
					{
     
		$opname=$rowprice->service; 
     
		$country=$rowprice->country; 
     
		$operator=$rowprice->oparetor; 
		$phonecode=$rowprice->c_code; 
		$opcode=$rowprice->opcode; 
     
		$priceid=$rowprice->id; 
     
		$prefix=$rowprice->prefix; 
		$rate=$rowprice->rate; 
     
		$pcode=$rowprice->pcode; 
     
		$comm=$rowprice->comm; 
		$charge=$rowprice->charge; 
		$type=$rowprice->type; 
	
	
		$query = $this->db->query("SELECT * from rate_com where tarif='$trif_id' and pcode='$pcode' and service='$opname' and prefix='$prefix'");


		if($query->num_rows() == 0 ) {
	
		$sqlratein="INSERT INTO `rate_com` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `tarif`, `userid`, `create_by`, `service`, `prefix`, `pcode`, `type`, `rate`, `comm`, `charge`, `price_id`, `status`, `date`) VALUES (NULL, '$country', '$phonecode', '$operator', '$opcode', '$trif_id', '$userid', '$sessuid', '$opname', '$prefix', '$pcode', '$type', '$rate', '$comm', '$charge', '$priceid', '1', '$create_date')";
	
		$this->db->query($sqlratein);
		}
	
		}
		
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= 'Edit Rate Sync Reseller: '.$residhex;
		$this->mit->InsertActivetUser($uids,$msg);
					
		

		if($ctype=="subadmin"){$level="subadmin";} 
		if($ctype=="reseller5"){$level="5";} 
		if($ctype=="reseller4"){$level="4";} 
		if($ctype=="reseller3"){$level="3";} 
		if($ctype=="reseller2"){$level="2";}
		if($ctype=="reseller1"){$level="1";}
	
		
		redirect('admin/resellers/'.$level, 'refresh');	 
		
	}
	
	
	public function rateSync($uid="") {
		/*
		$create_date = date('Y-m-d H:i:s');
		
		$member = $this->session->userdata('admin_session');
		
		$sessuid = $member->id;
		
		$residhex=$this->mdb->passwordChanger('decrypt', $uid); 
		
$trif_id = $this->db->get_where('reseller',array('username' =>$residhex))->row()->tarif;

$userid = $this->db->get_where('reseller',array('username' =>$residhex))->row()->id;

$ctype = $this->db->get_where('reseller',array('username' =>$residhex))->row()->custype;


		
		$sql353="SELECT * from price"; 
 
		$query2 = $this->db->query($sql353);
		foreach ($query2->result() as $rowprice)
					{
     
		$opname=$rowprice->service; 
     
		$country=$rowprice->country; 
     
		$operator=$rowprice->oparetor; 
		$phonecode=$rowprice->c_code; 
		$opcode=$rowprice->opcode; 
     
		$priceid=$rowprice->id; 
     
		$prefix=$rowprice->prefix; 
		$rate=$rowprice->rate; 
     
		$pcode=$rowprice->pcode; 
     
		$comm=$rowprice->comm; 
		$charge=$rowprice->charge; 
		$type=$rowprice->type; 
	
	
		$query = $this->db->query("SELECT * from rate_module where tarif='$trif_id' and pcode='$pcode' and service='$opname' and prefix='$prefix' and type='$type'");


		if($query->num_rows() == 0 ) {
	
		$sqlratein="INSERT INTO `rate_module` (`id`, `country`, `c_code`, `oparetor`, `opcode`, `tarif`, `userid`, `create_by`, `service`, `prefix`, `pcode`, `type`, `rate`, `comm`, `charge`, `price_id`, `status`, `date`) VALUES (NULL, '$country', '$phonecode', '$operator', '$opcode', '$trif_id', '$userid', '$sessuid', '$opname', '$prefix', '$pcode', '$type', '$rate', '$comm', '$charge', '$priceid', '1', '$create_date')";
	
		$this->db->query($sqlratein);
		}
	
		}
		
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= 'Edit Rate Sync Reseller: '.$residhex;
		$this->mit->InsertActivetUser($uids,$msg);
					
		

		if($ctype=="subadmin"){$level="subadmin";} 
		if($ctype=="reseller5"){$level="5";} 
		if($ctype=="reseller4"){$level="4";} 
		if($ctype=="reseller3"){$level="3";} 
		if($ctype=="reseller2"){$level="2";}
		if($ctype=="reseller1"){$level="1";}
	
		
	
		*/
			redirect('admin/resellers/'.$level, 'refresh');	 
		
	}

	public function payments($user="", $page="", $resel="", $from1="", $to1="", $paytype="", $addby="", $limit=""){

	$var=$this->input->post();	
	$varget=$this->input->get();	

	if($_POST){
	$resel = isset($var["resel"]) ? $var["resel"] : "";

	$from1 = isset($var["from1"]) ? $var["from1"] : "";
	$to1 = isset($var["to1"]) ? $var["to1"] : "";

	$paytype = isset($var["paytype"]) ? $var["paytype"] : "";
	$addby = isset($var["addby"]) ? $var["addby"] : "";

	$limit = isset($var["limit"]) ? $var["limit"] : "";
	}else {

	$resel = isset($varget["resel"]) ? $varget["resel"] : "";

	$from1 = isset($varget["from1"]) ? $varget["from1"] : "";
	$to1 = isset($varget["to1"]) ? $varget["to1"] : "";

	$paytype = isset($varget["paytype"]) ? $varget["paytype"] : "";
	$addby = isset($varget["addby"]) ? $varget["addby"] : "";

	$limit = isset($varget["limit"]) ? $varget["limit"] : "";

	}

	if(empty($limit) ) {
		 $limit = 10;
		 }

	$op = ""; // Initialize undefined variable
	$link="from1=$from1&to1=$to1&resel=$resel&op=$op&addby=$addby&paytype=$paytype&limit=$limit";
	$targetpage = "?";
	 //your file name  (the name of this file)
	$pageselect = isset($varget['pageid']) ? $varget['pageid'] : "";
		 //how many items to show per page
	$page = isset($varget['page']) ? $varget['page'] : "";
	if($page)  
	$start = ($page - 1) * $limit;       //first item to display on this page 
	else 
	$start = 0;                //if no page var is given, set start to 0 
		
	// data query start here

	 if($from1!="" and $to1!="" && $addby=="all" && $resel=="none" && $paytype=="all" ) 
  { 

  $sql="SELECT * FROM pay_receive WHERE idate >= '$from1' and idate <= '$to1' ORDER BY id desc LIMIT $start, $limit"; 

  $sqlcont="SELECT * FROM pay_receive WHERE idate >= '$from1' and idate <= '$to1' ORDER BY id desc"; 
   
  } 
  
   elseif($from1!="" and $to1!="" and $resel=="none" and $addby!="all" and $paytype=="all")  { 

  $sql="SELECT * FROM pay_receive WHERE sender='$addby' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc LIMIT $start, $limit"; 

 $sqlcont="SELECT * FROM pay_receive WHERE sender='$addby' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc"; 

  } 
  
   elseif($from1!="" and $to1!="" and $resel=="none" and $addby=="all" and $paytype!="all")  { 

  $sql="SELECT * FROM pay_receive WHERE type='$paytype' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc LIMIT $start, $limit"; 

  $sqlcont="SELECT * FROM pay_receive WHERE type='$paytype' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc"; 

  } 
  
  
  
  elseif($from1!="" and $to1!="" and $resel!="none" and $addby=="all" and $paytype=="all")  { 

  $sql="SELECT * FROM pay_receive WHERE userid='$resel' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc LIMIT $start, $limit"; 

 $sqlcont="SELECT * FROM pay_receive WHERE userid='$resel' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc"; 

  } 
   
   
  elseif($from1!="" and $to1!="" and $resel!="none" and $addby=="all" and $paytype!="all")  { 

  $sql="SELECT * FROM pay_receive WHERE userid='$resel' and type='$paytype' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc LIMIT $start, $limit"; 

 $sqlcont="SELECT * FROM pay_receive WHERE userid='$resel' and type='$paytype' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc"; 

  } 
   
  elseif($from1!="" and $to1!="" and $resel!="none" and $addby!="all" and $paytype=="all")  { 

  $sql="SELECT * FROM pay_receive WHERE userid='$resel' and sender='$addby' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc LIMIT $start, $limit"; 

  $sqlcont="SELECT * FROM pay_receive WHERE userid='$resel' and sender='$addby' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc"; 

  } 
   
  elseif($from1!="" and $to1!="" and $resel!="none" and $paytype!="all" and $addby!="all")  { 

  $sql="SELECT * FROM pay_receive WHERE userid='$resel' and type='$paytype' and sender='$addby' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc LIMIT $start, $limit";

  $sqlcont="SELECT * FROM pay_receive WHERE userid='$resel' and type='$paytype' and sender='$addby' and idate >= '$from1' and idate <= '$to1'  ORDER BY id desc"; 
  } 

 else { 

 	if(!empty($user)) {

 	$residhex=$this->mdb->passwordChanger('decrypt', $user); 

 	$sql="SELECT * from pay_receive where userid='$residhex' ORDER BY id desc LIMIT $start, $limit"; 
  	$sqlcont="SELECT * from pay_receive where userid='$residhex' ORDER BY id desc"; 

 	}else{

 	$sql="SELECT * from pay_receive where `sender`='Admin' or `desc`='auto' ORDER BY id desc LIMIT $start, $limit"; 
  	$sqlcont="SELECT * from pay_receive where `sender`='Admin' or `desc`='auto' ORDER BY id desc"; 

 	}
   
  
}
// data query finish

	$querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
	} 

	$username = "";
	if(!empty($resel) && $resel != "none") {
		$reseller_row = $this->db->get_where('reseller',array('id' =>$resel))->row();
		if($reseller_row) {
			$username = $reseller_row->username;
		}
	}

	$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);

	$query2 = $this->db->query($sql);

	$data['limit'] = $limit;
	
	$data['paytype'] = $paytype;
	$data['addby'] = $addby;

	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['resel'] = $resel;

	$data['links'] = $pagination;
	$data['payment_list'] = $query2->result();

	$data['page_title'] = ' Payments '. $username;

	$data['page_name'] = 'payment_admin_history';
	$this->load->view('admin/index', $data);
	}

	public function recharge($page="", $from1="", $to1="", $operator="", $op="", $limit="") {


	$var=$this->input->post();	
	$varget=$this->input->get();	

	if($_POST){
	$operator=$var["operator"]; 

	$from1=$var["from1"]; 
	$to1=$var["to1"]; 

	$op=$var["op"]; 
	
	$limit=$var["limit"]; 
	}else {

	$operator=$varget["operator"]; 

	$from1=$varget["from1"]; 
	$to1=$varget["to1"]; 

	$op=$varget["op"]; 
	
	$limit=$varget["limit"];
	
	}

	if(empty($limit) ) { 
		 $limit = 50; 
		 }


	$link="from1=$from1&to1=$to1&op=$op&operator=$operator&limit=$limit"; 
	$targetpage = "?"; 
	 //your file name  (the name of this file) 
	$pageselect=$varget['pageid']; 
		 //how many items to show per page 
	$page = $varget['page']; 
	if($page)  
	$start = ($page - 1) * $limit;       //first item to display on this page 
	else 
	$start = 0;                //if no page var is given, set start to 0 
		
	// data query start here

	if($from1!="" and $to1!="" and $op=="all" and $operator=="all"){ 
    $sql="select * from op_recharge where idate >= '$from1' and idate <= '$to1' order by id desc"; 

  
  } 
   
 // service  
  elseif($from1!="" and $to1!="" and $op!="all" and $operator=="all") 
  { 
  $sql="SELECT * from op_recharge where service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 

   $sqlcont="SELECT * from op_recharge where service='$op' and idate >= '$from1' and idate <= '$to1' order by id desc "; 

  }  
   
   // oparetor  
  elseif($from1!="" and $to1!="" and $op=="all" and $operator!="all") 
  { 
  $sql="SELECT * from op_recharge where telco='$operator' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit"; 

   $sqlcont="SELECT * from op_recharge where telco='$operator' and idate >= '$from1' and idate <= '$to1' order by id desc  "; 

  
  }  
 
  // oparetor  
  elseif($from1!="" and $to1!="" and $op!="all" and $operator!="all") 
  { 
  $sql="SELECT * from op_recharge where service='$op' and telco='$operator' and idate >= '$from1' and idate <= '$to1' order by id desc  LIMIT $start, $limit";
   
   $sqlcont="SELECT * from op_recharge where service='$op' and telco='$operator' and idate >= '$from1' and idate <= '$to1' order by id desc"; 
  }  
   
  else { 
   
   $sql="SELECT * from op_recharge order by id desc LIMIT $start, $limit"; 
 	$sqlcont="SELECT * from op_recharge order by id desc"; 
  
  }

  $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
	} 

	$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);

	$query2 = $this->db->query($sql);

	$data['limit'] = $limit;
	
	$data['operator'] = $operator;
	$data['op'] = $op;

	$data['to1'] = $to1;
	$data['from1'] = $from1;
	

	$data['links'] = $pagination;
	$data['recharge_list'] = $query2->result();

	$data['page_title'] = ' Recharge History ';

	$data['page_name'] = 'recharge_history';
	$this->load->view('admin/index', $data);


	}

	public function balance() {

	$data['page_title'] = ' Balance Report ';

	$data['page_name'] = 'balance_report';
	$this->load->view('admin/index', $data);


	}

	public function totalUse() {

	$var=$this->input->post();	
	$varget=$this->input->get();

	$idate=date('Y-m-d');	

	if($_POST){
	$resel=$var["resel"]; 

	$from1=$var["from1"]; 
	$to1=$var["to1"]; 	
	}

	

  	$data['to1'] = $to1;
	$data['from1'] = $from1;
	$data['resel'] = $resel;

	//$data['subamount'] = $subamount;
	//$data['subcostamount'] = $subcostamount;
	

	$data['page_title'] = 'Total Usages Report ';

	$data['page_name'] = 'total_use';
	$this->load->view('admin/index', $data);


	}
	
	public function Transaction($page="", $from1="", $to1="", $resel="", $notetr="", $limit=""){
	
	$var=$this->input->post();	
	$varget=$this->input->get();
	
	if($_POST){
	$resel=$var["resel"]; 

	$from1=$var["from1"]; 
	$to1=$var["to1"]; 

	$notetr=$var["notetr"]; 
	
	$limit=$var["limit"]; 
	}else {

	$resel=$varget["resel"]; 

	$from1=$varget["from1"]; 
	$to1=$varget["to1"]; 

	$notetr=$varget["notetr"]; 
	
	$limit=$varget["limit"];
	
	}
	
	if(empty($limit) ) { 
		 $limit = 50; 
		 }


	$link="from1=$from1&to1=$to1&resel=$resel&limit=$limit&notetr=$notetr"; 
	$targetpage = "?"; 
	 //your file name  (the name of this file) 
	$pageselect=$varget['pageid']; 
		 //how many items to show per page 
	$page = $varget['page']; 
	if($page)  
	$start = ($page - 1) * $limit;       //first item to display on this page 
	else 
	$start = 0;                //if no page var is given, set start to 0 
		
	// data query start here
	
	  if($from1!="" and $to1!="" && $resel=="none" && $notetr=="") { 
	  $sql="SELECT * FROM trans WHERE  date >= '$from1' and date <= '$to1'  ORDER BY id desc LIMIT $start, $limit"; 
	  $sqlcont="SELECT * from trans where date >= '$from1' and date <= '$to1'"; 
	  } 
	  else if($from1!="" and $to1!="" && $resel!="none" && $notetr=="") 
	  { 
	  $sql="SELECT * FROM trans WHERE  date >= '$from1' and date <= '$to1' and userid='$resel' ORDER BY id desc LIMIT $start, $limit"; 
	   $sqlcont="SELECT * from trans where date >= '$from1' and date <= '$to1' and userid='$resel'"; 
	  } 
	  else if($from1!="" and $to1!="" && $resel=="none" && $notetr!="") 
	  { 
	  $sql="SELECT * FROM trans WHERE  date >= '$from1' and date <= '$to1' and (`desc` LIKE '%$notetr%' or flex_id = '$notetr') ORDER BY id desc LIMIT $start, $limit"; 

	  $sqlcont="SELECT * from trans where date >= '$from1' and date <= '$to1' and (`desc` LIKE '%$notetr%' or flex_id = '$notetr')"; 
	  } 
	  else if($from1!="" and $to1!="" && $resel!="none" && $notetr!="") 
	  { 
	  $sql="SELECT * FROM trans WHERE  date >= '$from1' and date <= '$to1' and userid='$resel' and  (`desc` LIKE '%$notetr%' or flex_id = '$notetr') ORDER BY id desc LIMIT $start, $limit"; 
	   $sqlcont="SELECT * from trans where date >= '$from1' and date <= '$to1' and userid='$resel' and  (`desc` LIKE '%$notetr%' or flex_id = '$notetr')"; 
	  }else { 
	  $sql="select * from trans order by id desc LIMIT $start,$limit"; 
	  
	  $sqlcont="SELECT * from trans"; 
	  }
 
	  $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
	} 

	$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);

	$query2 = $this->db->query($sql);

	$data['limit'] = $limit;
	
	$data['resel'] = $resel;
	$data['notetr'] = $notetr;

	$data['to1'] = $to1;
	$data['from1'] = $from1;
	

	$data['links'] = $pagination;
	$data['trns_list'] = $query2->result();
	
	
	$data['page_title'] = 'Total Transaction Report ';
	$data['page_name'] = 'transaction';
	$this->load->view('admin/index', $data);

		
	}
	
	
	
		public function Transactioninfo($page="", $from1="", $to1="", $resel="", $notetr="", $limit=""){
	
	$var=$this->input->post();	
	$varget=$this->input->get();
	
	if($_POST){


	$notetr=$var["notetr"]; 
	
	$limit=$var["limit"]; 
	}else {



	$notetr=$varget["notetr"]; 
	
	$limit=$varget["limit"];
	
	}
	
	if(empty($limit) ) { 
		 $limit = 50; 
		 }


	$link="from1=$from1&to1=$to1&resel=$resel&limit=$limit&notetr=$notetr"; 
	$targetpage = "?"; 
	 //your file name  (the name of this file) 
	$pageselect=$varget['pageid']; 
		 //how many items to show per page 
	$page = $varget['page']; 
	if($page)  
	$start = ($page - 1) * $limit;       //first item to display on this page 
	else 
	$start = 0;                //if no page var is given, set start to 0 
		
	// data query start here
	
	  if(!empty($notetr)) { 
	      $notetr=$this->mdb->passwordChanger('encrypt', $notetr);
	  $sql="SELECT * FROM trnx WHERE trnx = '$notetr' ORDER BY id desc LIMIT $start, $limit"; 
	   $sqlcont="SELECT * FROM trnx WHERE trnx = '$notetr'"; 
	  }else { 
	  $sql="select * from trnx order by id desc LIMIT $start,$limit"; 
	  
	  $sqlcont="SELECT * from trnx"; 
	  }
 
	  $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
	} 

	$pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);

	$query2 = $this->db->query($sql);

	$data['limit'] = $limit;
	

	$data['notetr'] = $notetr;


	$data['links'] = $pagination;
	$data['trns_list'] = $query2->result();
	
	
	$data['page_title'] = 'Trnx Report ';
	$data['page_name'] = 'trnsinfo';
	$this->load->view('admin/index', $data);

		
	}
	
	
	

	public function serviceModual($para1="", $para2="", $para3="") {



	if($para1=="add") {

		if($_POST){

			$var=$this->input->post();
    	$this->mdb->insert('module',$var);
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Added Service By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
    	 redirect('admin/serviceModual', 'refresh');

		}else {

	$data['page_title'] = ' Service Modual Add';
	$data['page_name'] = 'service_add';


		}


	}

	else if($para1=="edit"){

	$data['servicid'] = $para2;

	$data['page_title'] = ' Service Modual Edit';
	$data['page_name'] = 'service_edit';

	}else if($para1=="update"){


		$var=$this->input->post();
	
    	$this->mdb->update('module',$var,array('id'=>$para2));
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Edit Service By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

    
		redirect('admin/serviceModual', 'refresh');

	}else if ($para1=="change") {

		if($para2=="active") {
			$change = array('status' =>1);
			}else {
			$change = array('status' =>0);
			}

			$this->mdb->update('module',$change,array('id'=>$para3));
			
			$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Service Status Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		redirect('admin/serviceModual', 'refresh');

		

	}else {

	$data['page_title'] = ' Service Modual ';

	$data['page_name'] = 'service_list';
	}
	$this->load->view('admin/index', $data);


	}

	public function rateModual($para1="", $para2="") {


	if($para1=="update"){

		if($_POST){

		$var=$this->input->post();
		$tarpcode=$var['tarpcode']; 
		$edit_com=$var['edit_com']; 
		$edit_rate=$var['edit_rate'];
        $edit_charge=$var['edit_charge']; 
		$tarifid=$var['tarifid']; 
		$cnt=count($var['chkbox']); 
		
		for($i=0;$i<$cnt;$i++) 
		{ 
		$del_id=$var['chkbox'];

		$queryrate="UPDATE  `price` SET `rate` =  '$edit_rate[$i]', `comm` =  '$edit_com[$i]', 
		`charge` =  '$edit_charge[$i]' where id='$del_id[$i]'"; 

		$this->db->query($queryrate);
		}
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Rate Modual Update Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		redirect('admin/rateModual', 'refresh');
	}

	}else {

	$sql="select * from price order by id asc"; 
		 
	$query11 = $this->db->query($sql);		
	$reselerrate = $query11->result() ;
		
	$data["all_rate"] = $reselerrate;

	$data['page_title'] = ' Rate Modual ';
	$data['page_name'] = 'admin_rate';
	}
	$this->load->view('admin/index', $data);

	}

	public function deposit($para1="", $para2="") {


	if($para1=="update"){

		if($_POST){

		$var=$this->input->post();
		$bkash=$var['bkash']; 
		$nagad=$var['nagad']; 
		$rocket=$var['rocket'];
       $account=$var['account'];
       $upay=$var['upay'];
        $b_bkash=$var['b_bkash']; 
		$b_nagad=$var['b_nagad']; 
		$b_rocket=$var['b_rocket'];
        $b_upay=$var['b_upay'];
        
        $d_bkash=$var['d_bkash']; 
		$d_nagad=$var['d_nagad']; 
		$d_rocket=$var['d_rocket'];
        $d_upay=$var['d_upay'];
        
       $level=$var['level'];  
       $self_price=$var['self_price'];
		$cnt=count($var['id']); 
		
		for($i=0;$i<$cnt;$i++) 
		{ 
		$del_id=$var['id'];

		$queryrate="UPDATE `level_list` SET `rocket` =  '$rocket[$i]', `bKash` =  '$bkash[$i]', 
		`NAGAD` =  '$nagad[$i]',`upay` =  '$upay[$i]' ,`account` =  '$account[$i]',`b_rocket` =  '$b_rocket[$i]', `b_bKash` =  '$b_bkash[$i]', 
		`b_NAGAD` =  '$b_nagad[$i]',`b_upay` =  '$b_upay[$i]',`d_rocket` =  '$d_rocket[$i]', `d_bKash` =  '$d_bkash[$i]', 
		`d_NAGAD` =  '$d_nagad[$i]',`d_upay` =  '$d_upay[$i]' ,`real_name` =  '$level[$i]',`self_price` =  '$self_price[$i]' where id='$del_id[$i]'"; 

		$this->db->query($queryrate);
		}
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Rate Modual Update Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		redirect('admin/deposit', 'refresh');
	}

	}else if($para1=="reset"){
	     $this->db->query("UPDATE `level_list` SET `real_name` = 'subadmin' WHERE `level_list`.`id` = 1");
$this->db->query("UPDATE `level_list` SET `real_name` = 'HOUSE' WHERE `level_list`.`id` = 2");
$this->db->query("UPDATE `level_list` SET `real_name` = 'DGM' WHERE `level_list`.`id` = 3");
$this->db->query("UPDATE `level_list` SET `real_name` = 'Dealer' WHERE `level_list`.`id` = 4");
$this->db->query("UPDATE `level_list` SET `real_name` = 'Seller' WHERE `level_list`.`id` = 5");
$this->db->query("UPDATE `level_list` SET `real_name` = 'Retailer' WHERE `level_list`.`id` = 6");
	   redirect('admin/deposit', 'refresh'); 
	}else {



	$data['page_title'] = 'Deposite Comission';
	$data['page_name'] = 'deposite_com';
	}
	$this->load->view('admin/index', $data);

	}


public function bkash_Get_Token(){

 $checkoutv=$this->mdb->getData('gateway',array('id'=>1));
			    
			    if($checkoutv[0]['type']==0){
			        if($checkoutv[0]['fnc_rate']==2){
    $tokenURL="https://tokenized.sandbox.bka.sh/v1.2.0-beta//tokenized/checkout/token/grant";
         
     }else{
			        
			        $tokenURL="https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/token/grant";
     }
			        
			    }else{
			        
			         if($checkoutv[0]['fnc_rate']==2){
    $tokenURL="https://tokenized.pay.bka.sh/v1.2.0-beta//tokenized/checkout/token/grant";
         
     }else{
			        
			        
			       $tokenURL="https://checkout.pay.bka.sh/v1.2.0-beta/checkout/token/grant";}
			        
			    } 
			        
			        
			     $username=$checkoutv[0]['userid']; //demo
    $app_secret= $checkoutv[0]['private_key'];
   $app_key= $checkoutv[0]['public_key'];    
	 $password= $checkoutv[0]['fnc_number'];		        
			        
			        


	$post_token=array(
        'app_key'=>$app_key,                                              
		'app_secret'=>$app_secret                  
	);	
    
    $url=curl_init($tokenURL);

	$posttoken=json_encode($post_token);
	$header=array(
		'Content-Type:application/json',
		'password:'.$password,                                                               
        'username:'.$username                                                           
    );				
    
    curl_setopt($url,CURLOPT_HTTPHEADER, $header);
	curl_setopt($url,CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($url,CURLOPT_RETURNTRANSFER, true);
	curl_setopt($url,CURLOPT_POSTFIELDS, $posttoken);
	curl_setopt($url,CURLOPT_FOLLOWLOCATION, 1);

	$resultdata=curl_exec($url);
	curl_close($url);

	
	return json_decode($resultdata, true);    
} 

 public function bkash_refund($amount="",$payid="",$trxID=""){
 $checkoutv=$this->mdb->getData('gateway',array('id'=>1));
if($checkoutv[0]['type']==0){
     if($checkoutv[0]['fnc_rate']==2){
    $createURL="https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/payment/refund";
         
     }else{
			        $createURL="https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/refund";}
			        
			    }else{
			        
			        if($checkoutv[0]['fnc_rate']==2){
    $createURL="https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/payment/refund";
         
     }else{$createURL="https://checkout.pay.bka.sh/v1.2.0-beta/checkout/payment/refund";}
			        
			    } 
    
    
 // must be unique
$intent = "sale";
$request_token=$this->bkash_Get_Token();
$token=$request_token['id_token'];


    $createpaybody=array('amount'=>$amount, 'paymentID'=>$payid, 'trxID'=>$trxID,'sku'=>'Refund','reason'=>'Refund');   
    $url = curl_init($createURL);

    $createpaybodyx = json_encode($createpaybody);

    $header=array(
        'Content-Type:application/json',
        'authorization:'.$token,
        'x-app-key:'.$checkoutv[0]['public_key']    
    );

    curl_setopt($url,CURLOPT_HTTPHEADER, $header);
	curl_setopt($url,CURLOPT_CUSTOMREQUEST, "POST");
	curl_setopt($url,CURLOPT_RETURNTRANSFER, true);
	curl_setopt($url,CURLOPT_POSTFIELDS, $createpaybodyx);
    curl_setopt($url,CURLOPT_FOLLOWLOCATION, 1);
    
    $resultdata = curl_exec($url);
   file_put_contents("refund.txt", $resultdata);
    curl_close($url);
    return $resultdata;   
 } 

  
  public function bkash_refund_status($amount="",$payid="",$trxID=""){
 $checkoutv=$this->mdb->getData('gateway',array('id'=>1));
if($checkoutv[0]['type']==0){
     if($checkoutv[0]['fnc_rate']==2){
    $createURL="https://tokenized.sandbox.bka.sh/v1.2.0-beta/tokenized/checkout/payment/refund";
         
     }else{
			        $createURL="https://checkout.sandbox.bka.sh/v1.2.0-beta/checkout/payment/refund";}
			        
			    }else{
			        
			        if($checkoutv[0]['fnc_rate']==2){
    $createURL="https://tokenized.pay.bka.sh/v1.2.0-beta/tokenized/checkout/payment/refund";
         
     }else{$createURL="https://checkout.pay.bka.sh/v1.2.0-beta/checkout/payment/refund";}
			        
			    } 
    
    
 // must be unique
$intent = "sale";
$request_token=$this->bkash_Get_Token();
$token=$request_token['id_token'];


    $createpaybody=array('paymentID'=>$payid, 'trxID'=>$trxID);   
    $url = curl_init($createURL);

    $createpaybodyx = json_encode($createpaybody);

    $header=array(
        'Content-Type:application/json',
        'authorization:'.$token,
        'x-app-key:'.$checkoutv[0]['public_key']    
    );

    curl_setopt($url,CURLOPT_HTTPHEADER, $header);
	curl_setopt($url,CURLOPT_CUSTOMREQUEST, "POST");
	curl_setopt($url,CURLOPT_RETURNTRANSFER, true);
	curl_setopt($url,CURLOPT_POSTFIELDS, $createpaybodyx);
    curl_setopt($url,CURLOPT_FOLLOWLOCATION, 1);
    
    $resultdata = curl_exec($url);
   //file_put_contents("refund.txt", $resultdata);
    curl_close($url);
    return $resultdata;   
 } 

  
  
	public function trnxu($id="",$type="") {

		$id=$this->mdb->passwordChanger('decrypt', $id);
if(empty($type)){
		$sqlap="UPDATE `trnx` SET  `status` ='1' WHERE `id` ='$id'"; 
					
					$this->db->query($sqlap);
					}else{
					
 
 $useri = $this->db->get_where('trnx',array('id' =>$id))->row()->userid;
					$amount = $this->db->get_where('trnx',array('id' =>$id))->row()->amount;
					$xid = $this->db->get_where('trnx',array('id' =>$id))->row()->trnx;
					$trnx=$this->mdb->passwordChanger('decrypt', $xid);
					 $res = $this->db->get_where('invoice',array('order_number' =>$trnx))->row()->response;
					 $p_git = json_decode($res);
$trnxid=$p_git->trxID;
$payid=$p_git->paymentID;
  $this->bkash_refund($amount,$payid,$trnxid);
				 $this->bkash_refund_status($amount,$payid,$trnxid) ;
					$this->mit->balanceUpdate($useri, $amount,"minus","main");  
					
					}

    	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Security Status Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

    	redirect('admin/Transactioninfo', 'refresh');

	

	$data['page_title'] = 'Security Modual ';
	$data['page_name'] = 'trnsinfo';
	$this->load->view('admin/index', $data);


	}




	public function security() {

		if($_POST){

		$var=$this->input->post();

		$session_time_out = $var['session_time_out'] ;

		 if($session_time_out>=10) {}else{
		 	redirect('admin/security', 'refresh');
		 } 

    	$this->mdb->update('security_option',$var,array('id'=>1));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Security Status Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

    	redirect('admin/security', 'refresh');

		}

	$data['page_title'] = 'Security Modual ';
	$data['page_name'] = 'security';
	$this->load->view('admin/index', $data);


	}
	
	public function api_balance($id) {
		
		$sql_api="SELECT * FROM `api_set` WHERE id='$id' and status=1"; 

				$queryapifl = $this->db->query($sql_api);
				foreach ($queryapifl->result() as $row_api)
					{

				$api_id=$row_api->id; 
				$provider=$row_api->provider; 
				$flapi_userid=$row_api->userid; 
				$flapi_key=$row_api->api_key; 

				$api_url=$row_api->url; 
				$apirespons=$row_api->response; 
					}
					
				
			
if($provider==11){
    
 	$url_send ="http://".$api_url."/myportal/api/rechargeapi/recharge_api_thirdparty.php?access_id=$flapi_userid&access_pass=$flapi_key&service=BLCK";
				//$postdata = json_encode($data);
				
				
				$api_status=$this->mdb->sendPostData($url_send,$data);
				echo $api_status;
				
				$apists=json_decode($api_status);
				$responsests = $apists->STATUS;
				$balance = $apists->MAIN_BALANCE;   
    
}else{
    
    	$data = array(
				  "user" => $flapi_userid,
				  "key" => $flapi_key
				);
				
    
				$url_send ="http://".$api_url."/sendapi/balance";
				//$postdata = json_encode($data);
				
				  $header=array(
    'api-key: '.$flapi_key.'',
    'api-user: '.$flapi_userid.''
);
				$api_status=$this->mdb->sendPostData($url_send,$data,$header);
				echo $api_status;
				
				$apists=json_decode($api_status);
				$responsests = $apists->status;
				$balance = $apists->balance;
}
				if($responsests==1 or $responsests=='OK') 
                        { 
					
				$sqlap="UPDATE  `api_set` SET  `balance` =  '$balance', `bal_response`='$api_status' WHERE  `api_set`.`id` ='$id'"; 
					
					$this->db->query($sqlap);
					
						}
						
						redirect('admin/apiset', 'refresh');
		
	}

	public function apiset($para1="", $para2="") {

	if($para1=="add"){

		if($_POST){
		$ip = $_SERVER['REMOTE_ADDR']; 

		$var=$this->input->post();

		
		
		$api_title=$var['api_title'];
		$provider=$var['apitypes'];
		$userid=$var['userid'];
		$url=$var['url'];
		$bal_url=$var['bal_url'];
		$resurl=$var['resurl'];
		$response=$var['response'];
		$status=$var['status'];
		$api_key=$var['api_key'];
		
		
		$url_peramiter=$var['url_peramiter'];
		$resurl_peramiter=$var['resurl_peramiter'];

		$this->db->select('*');
		$this->db->from('api_set');
		$this->db->where('api_title', $api_title);			
		$query=$this->db->get(); 
				
		if($query->num_rows()>0) {

		$this->session->set_flashdata('error', 'API Allready Exists');
		$data['page_title'] = 'API Add ';
		$data['page_name'] = 'apiadd';

		} 
		else{ 
		$sql="INSERT INTO `api_set` (`id`, `api_title`, `provider`, `userid`, `api_key`, `url`, `url_peramiter`, `port`, `bal_url`, `response`, `resurl`, `resurl_peramiter`, `status`, `date`) VALUES (NULL, '$api_title', '$provider', '$userid', '$api_key', '$url', '$url_peramiter', '$port', '$bal_url', '$response', '$resurl', '$resurl_peramiter', '$status', '$create_date')"; 
		
		$result=$this->db->query($sql) ; 
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Added Api By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		$this->session->set_flashdata('success', 'Add API Successfully');
		redirect('admin/apiset', 'refresh');


		}// 
	}else {  // post end
	$data['page_title'] = 'API Add ';
	$data['page_name'] = 'apiadd';
	}

	}else if ($para1=="delete") {

		 $parentcat =  $this->db->get_where('routing',array('route' => $para2))->row()->title;
		 if(!empty($parentcat)) {
        $this->session->set_flashdata('error', 'API Not Delete');
         redirect('admin/apiset', 'refresh');

        }else {
		$this->mdb->delete('api_set',array('id' => $para2));
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Delete Api By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		$this->session->set_flashdata('success', 'Add API Successfully');
		redirect('admin/apiset', 'refresh');
		}	
	
	}
	else if ($para1=="update") {
		$var=$this->input->post();
		$this->mdb->update('api_set',$var, array('id' => $para2));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Update Api By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		
		$this->session->set_flashdata('success', 'Update Successfully');
		redirect('admin/apiset', 'refresh');	
	
	}else if ($para1=="edit") {


	$data['eid'] = $para2;
	$data['page_title'] = 'API Settings ';
	$data['page_name'] = 'api_edit';	
	

	}else {
	$data['page_title'] = 'API Settings ';
	$data['page_name'] = 'apiset';	
	}

	$this->load->view('admin/index', $data);


	}

		public function route($para1="", $para2="") {

		$idate=date('Y-m-d');
		$create_date=date('Y-m-d H:i:s');

		if($para1=="add"){

		if($_POST){

		$var=$this->input->post();

		$title=$var['title'];
		$route=$var['route'];
		$operator=$var['operator'];
		$code=$var['code'];
		$priority=$var['priority'];
		$sellrate=$var['sellrate'];
		$active=$var['active'];
		$status=$var['status'];
		$send_prefix=$var['send_prefix'];
		$my_prefix=$var['my_prefix'];
		$price=$var['rate'];

		$sql="INSERT INTO `routing` (`id`, `title`, `route`, `service`, `pcode`, `priority`, `price`, `sell`, `my_prefix`, `send_prefix`, `active`, `status`, `date`, `time`) VALUES (NULL, '$title', '$route', '$operator', '$code', '$priority', '$price', '$sellrate', '$my_prefix', '$send_prefix', '$active', '$status', '$idate', '$create_date')";

		$query11 = $this->db->query($sql);	
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Added Route By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		$this->session->set_flashdata('success', ' Added Route Successfully');
		redirect('admin/route', 'refresh');

			}


		$data['page_title'] = 'Route Add';
		$data['page_name'] = 'route_add';	

		}else if ($para1=="update") {

		if($_POST){

		$var=$this->input->post();

		$title=$var['title'];
		$route=$var['route'];
		$operator=$var['operator'];
		$code=$var['code'];
		$priority=$var['priority'];
		$sellrate=$var['sellrate'];
		$active=$var['active'];
		$status=$var['status'];
		$send_prefix=$var['send_prefix'];

		$sql="UPDATE  `routing` SET  `title` =  '$title', 
		`route` =  '$route', 
		`service` =  '$operator', 
		`pcode` =  '$code', 
		`priority` =  '$priority', 
		`send_prefix` =  '$send_prefix', 
		`active` =  '$active', 
		`status` =  '$status' WHERE  `routing`.`id` ='$para2'"; 
		$query11 = $this->db->query($sql);		
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Update Route By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		$this->session->set_flashdata('success', 'Route Update Successfully');
		redirect('admin/route', 'refresh');
		}
		}else if ($para1=="edit") {
		
		$data["idsl"] = $para2;
		$data['page_title'] = 'Route Edit';
		$data['page_name'] = 'route_edit';

	
		}else if ($para1=="active") {

		$routeupdate = array('active' =>1);
		$this->mdb->update('routing',$routeupdate,array('id'=>$para2));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Route Status Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		
		$this->session->set_flashdata('success', ' Update Successfully');
		 redirect('admin/route', 'refresh');

		}else if ($para1=="deactive") {

		$routeupdate = array('active' =>0);
		$this->mdb->update('routing',$routeupdate,array('id'=>$para2));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Route Status Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		$this->session->set_flashdata('success', ' Update Successfully');
		redirect('admin/route', 'refresh');

		}else if ($para1=="delete") {

		$this->mdb->delete('routing',array('id'=>$para2));
		$this->session->set_flashdata('success', ' Delete Successfully');

		redirect('admin/route', 'refresh');

		}
		else if ($para1=="view") {

		$sql="SELECT * from routing where route='$para2'"; 

		$query11 = $this->db->query($sql);		
		$routelist = $query11->result();
			
		$data["all_routing"] = $routelist;
		$data['page_title'] = 'Route Settings List';
		$data['page_name'] = 'route';	

		}else {
		$sql="SELECT * from routing order by id desc"; 

		$query11 = $this->db->query($sql);		
		$routelist = $query11->result();
			
		$data["all_routing"] = $routelist;
		$data['page_title'] = 'Route Settings List';
		$data['page_name'] = 'route';	
		}
			

	$this->load->view('admin/index', $data);

	}

	public function chooseRoute()
	{
		$var=$this->input->get();

		$data['id'] = $var['operator'];
		
		$this->load->view('admin/choose_routing', $data);
	
	}

	public function chooseCodeRoute()
	{
		$var=$this->input->get();

		$data['ids'] = $var['code'];
		
		$this->load->view('admin/choose_code_routing', $data);
	
	}


	public function delAcc ($para1="", $para2="") {



	if($para1=="restore") {

	$change = array('status' =>1);
	$this->mdb->update('reseller',$change,array('id'=>$para2));
    redirect('admin/delAcc', 'refresh');

	}

	 if($_POST){

	$var=$this->input->post();

	$cnt=count($var['chkbox']); 
		
	for($i=0;$i<$cnt;$i++) 
	{ 
	$del_id=$var['chkbox'];
	$change = array('status' =>1);
	$this->mdb->update('reseller',$change,array('id'=>$del_id[$i]));
	}
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Reseller Restore By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		
    redirect('admin/delAcc', 'refresh');
	}else {


	$sql="SELECT * from reseller where custype!='admin' AND status=2 order by id desc"; 

	$query11 = $this->db->query($sql);		
	$reselerdel = $query11->result() ;
		
	$data["all_del_reseller"] = $reselerdel;

	$data['page_title'] = 'Delete Resellers ';
	$data['page_name'] = 'reseller_del_list';
	}
	$this->load->view('admin/index', $data);
	}

	public function brand () {

	if($_POST){

	$var=$this->input->post();
	$this->mdb->update('company',$var,array('id'=>1));

	redirect('admin/brand', 'refresh');

	}else {

	$data['page_title'] = 'Branding ';
	$data['page_name'] = 'brand';
	}
	$this->load->view('admin/index', $data);
	}

	public function deviceLogs() {

	$var=$this->input->post();	
	
	$varget=$this->input->get();

	$action=$var["action"];
	$resel=$var["resel"];
	
	$cnt=count($var['id']); 	

	if(isset($action)) {
		if($action=='inactive') {$stsac='0';}
		if($action=='delete') {$stsac='2';}
		if($action=='active') {$stsac='1';}

		for($i=0;$i<$cnt;$i++) { 
		 $del_id=$var['id'][$i]; 
		 
		 $userids = $this->db->get_where('device_list',array('id' =>$del_id))->row()->userid;
		 
		  $devid = $this->db->get_where('device_list',array('id' =>$del_id))->row()->tokenid;
		 
		  $reselleri = $this->db->get_where('reseller',array('id' =>$userids))->row()->username;
		 
		 $query="UPDATE  `device_list` SET  `status` =  '$stsac' where id='$del_id'"; 
		    $this->db->query($query);

		    if($action=='delete') {

		    	$this->db->where('id', $del_id);
		    	$this->db->delete('device_list');

		    }
			
		$user=$this->session->userdata('admin_session');
		$uids= $user->id;
		$msg= ucfirst($action).' Device Reseller: '.$reselleri.' Device: '.$devid;
		$this->mit->InsertActivetUser($uids,$msg);
		
		}
		
		
		
		print("<script>
	   	window.opener.location.reload();
	   
		</script>");
	}

	$data['resel'] = $resel;

	//$userid = $this->db->get_where('reseller',array('username' =>$cname))->row()->id;

	if(!empty($resel) && $resel!="none") {
	$sql="select * from device_list where userid='$resel' and userid!=1 order by id desc"; 	
	}else if($resel=="none") {	
	$sql="select * from device_list where userid!=1  order by id desc"; 	
	}else {
	$sql="select * from device_list where userid!=1  order by id desc"; 
	}



	$query11 = $this->db->query($sql);		
	$reselerdel = $query11->result() ;
		
	$data["device_list"] = $reselerdel;

	$data['page_title'] = 'Device Logs ';
	$data['page_name'] = 'device_log';
	
	$this->load->view('admin/index', $data);

	}
	
	public function device_action($para1="", $para2="", $para3="") {
		
		
		$devid=$this->mdb->passwordChanger('decrypt', $para2);
		
		if($para1=='allow') {$stsac=1;}
		if($para1=='disable') {$stsac=0;}
		
		$query="UPDATE  `device_list` SET  `status` = '$stsac' where id='$devid'"; 
		 
		$this->db->query($query);
		
		if($para1=='delete') {
		
		$this->db->where('id', $devid);
		$this->db->delete('device_list');

		}
		
		redirect('admin/deviceLogs', 'refresh');
	}

	public function notice_update($action="",$noticeid="", $page="", $limit="") {

		$var=$this->input->post();	
		$varget=$this->input->get();	

		if($_POST){
         
$newnotice= $var['notice'];
		$newnotice = str_replace("'", '', $newnotice);
		
		$limit=$var["limit"];
		
		
		} 


		else {

		
		$limit=$varget["limit"]; 
		
	}
if($action=='delete'){
   $decideid = $this->mdb->passwordChanger('decrypt', $noticeid); 
$this->db->where('id', $decideid);
		$this->db->delete('notice');

          	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'delete notice By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
     
      redirect('admin/notice_update', 'refresh');

}
	
		if(empty($limit) ) { 
		 $limit = 50; 
		 }

		 $link="from1=$from1&to1=$to1&number=$number&op=$op&opcompany=$opcompany&limit=$limit"; 

		 $targetpage = "?"; 
		 //your file name  (the name of this file) 
		 $pageselect=$varget['pageid']; 
		 //how many items to show per page 
		 $page = $varget['page']; 
		 if($page)  
		$start = ($page - 1) * $limit;       //first item to display on this page 
		 else 
		$start = 0;             
     
      
	if(!empty($var['save'])){
	$var=$this->input->post();
	
      $decideid = $this->mdb->passwordChanger('decrypt', $noticeid); 
	echo "ok";
	$queryuuee="UPDATE  `notice` SET  `notice` =  '$newnotice' WHERE `id` ='$decideid'"; 
 	$query = $this->db->query($queryuuee);
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Update Notice By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
 	redirect('admin/notice_update/edit/'.$noticeid.'', 'refresh');
	}
      if($var['add']){
	$var=$this->input->post();
	$create_date=date('j F Y g:i A'); 
      	$sql = "INSERT INTO `notice` (`date`,`notice`,`subject`,`member`,`service`,`status`,`cdate`) VALUES ('$create_date','$newnotice','','','','','')";
	
		$this->db->query($sql);
          
          
          	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add notice By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
     
      redirect('admin/notice_update', 'refresh');
	}
      
      
      
      
       $sql="select * from notice order by id desc LIMIT $start,$limit";

			 $sqlcont="select * from notice order by id desc";

      $querycount = $this->db->query($sqlcont);
		  if($querycount->num_rows() > 0 ) {
		  $total_pages = $querycount->num_rows;
		  } 

		 $pagination = $this->mdb->pagelink($page,$total_pages,$limit,$link);

		$query2 = $this->db->query($sql);
      
      $data['limit'] = $limit;

		$data['start'] = $start;
      $data['total_pagescount'] = $total_pages;

	
		$data['links'] = $pagination;
		$data['notice_history'] = $query2->result();
      $data['action'] = $action;
       $data['id'] = $noticeid;
	$data['page_title'] = 'Reseller Notice ';
	$data['page_name'] = 'reseller_notice';
	$this->load->view('admin/index', $data);

	}

	//// login notice

	

	public function loginotice() {

	

	if($_POST){

	$var=$this->input->post();

	//$newnotice=$var['notice'];

	$idate= date('Y-m-d H:i:s');

	$newnotice = $this->security->xss_clean($this->input->post('notice'));

	$queryuuee="UPDATE  `login_notice` SET `lnotice` =  '$newnotice', `date` =  '$idate' WHERE  `login_notice`.`id` ='1'"; 

 	$query = $this->db->query($queryuuee);
	
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Update Login Notice By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

	//	$this->mdb->update('login_notice',$var,array('id'=>1));

	redirect('admin/loginotice', 'refresh');

	}else {
	$data['page_title'] = 'Login Notice ';
	$data['page_name'] = 'notice';
	$this->load->view('admin/index', $data);

	}

	


	}

	public function noticeframe() {
	$this->load->view('admin/noticeframe');

	}



	public function pin()
	{

		if($this->input->post()){
		 $get=$this->input->post();
	 	$user=$this->session->userdata('admin_session');
        $chk_data=$this->mdb->getData('reseller',array('id'=>$user->id));
		$chkpass=$chk_data[0]['pincode'];
		$nwpass = $get['oldpass'];
		$chk= $this->mdb->verify($nwpass,$chkpass); 
		if($chk==true) {
		//$this->session->set_flashdata('passerror', 'Your Password Worng');
		
		if($get['newpass']==$get['rnewpass']){
		$this->mdb->update('reseller',array('pincode'=>$this->mdb->generate($get['newpass'])),array('id'=>$user->id));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Salf PIN Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		
		$this->session->set_flashdata('success', 'Your PIN Change Update');
		}else {
		$this->session->set_flashdata('error', 'Your Confirm PIN Worng');
		}
		}else {
		$this->session->set_flashdata('error', 'Your Old PIN Worng');
		}
		
		redirect('admin/pin', 'refresh');
		}else {
		$data['page_title'] = 'My PIN';
        $data['page_name'] = 'pin';
		$this->load->view('admin/index', $data);
		}
        

		
	}
	
	 
	 
	 public function password()
	{

		if($this->input->post()){
		 $get=$this->input->post();
	 	$user=$this->session->userdata('admin_session');
        $chk_data=$this->mdb->getData('reseller',array('id'=>$user->id));
		$chkpass=$chk_data[0]['password'];
		$nwpass = $get['oldpass'];
		$chk= $this->mdb->verify($nwpass,$chkpass); 
		if($chk==true) {
		//$this->session->set_flashdata('passerror', 'Your Password Worng');
		
		if($get['newpass']==$get['rnewpass']){
		$this->mdb->update('reseller',array('password'=>$this->mdb->generate($get['newpass'])),array('id'=>$user->id));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Salf Password Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		$this->session->set_flashdata('success', 'Your Password Change Update');
		}else {
		$this->session->set_flashdata('error', 'Your Confirm Password Worng');
		}
		}else {
		$this->session->set_flashdata('error', 'Your Old Password Worng');
		}
		
		redirect('admin/password', 'refresh');
		}else {
		$data['page_title'] = 'My Password';
        $data['page_name'] = 'password';
		$this->load->view('admin/index', $data);
		}
        

		
	}

	  public function profile() {
		  
		$member = $this->session->userdata('admin_session');
		
		$uid= $member->id;
		  
		if($this->input->post()){
		$var=$this->input->post();
		
		$username = $var['username'];
		$name = $var['name'];
		$mobile = $var['mobile'];
		$email = $var['email'];
		
		$olduser = $this->db->get_where('reseller',array('id' =>$uid))->row()->username;
		if($olduser!=$username) {
			
			$this->form_validation->set_rules('username', 'Username', 'is_unique[reseller.username]|trim|required|xss_clean|min_length[5]');
			if ($this->form_validation->run() == FALSE) {
			
			$this->session->set_flashdata('error', 'Your Admin Username Allready Exist');

			redirect('admin/profile', 'refresh');
		
			}else {
			$usupdate="UPDATE `reseller` SET  
			`username` =  '$username' WHERE `id` ='$uid'"; 
			
			$this->db->query($usupdate);
			
			$user=$this->session->userdata('admin_session');
			$uid= $user->id;
			$msg= 'Admin Username Change '.$username.' By Admin';
			$this->mit->InsertActivetUser($uid,$msg);
			}
		}
		
		
		
		$query="UPDATE `reseller` SET  
		`name` =  '$name', 
		`mobile` =  '$mobile', 
		`email` =  '$email' WHERE `id` ='$uid'"; 
		
		$this->db->query($query);
			
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Salf Profile Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

		$this->session->set_flashdata('success', 'Your Profile Update Successfully');

		redirect('admin/profile', 'refresh');
		
		}else {
		
		$data['page_title'] = 'My Profile';
		$data['page_name'] = 'profile';
		}
		$this->load->view('admin/index', $data);
		  
	  }
	  
	 public function setnumberverify() {
		
		$adminu = $this->session->userdata('admin_session');
		$uid= $adminu->id;
		$var= $this->input->post();
		
		$queryalowip= $this->db->query("select * from sadmin_phone where uid='$uid'");
	
		if($queryalowip->num_rows() > 0 ) {
			redirect('admin', 'refresh');	
		}
		
		if($_POST){
		$mobile=$var['mobile'];
		$code=$var['code'];		
			
		$getverydata= $this->session->userdata('mobilechk');
		$vmobile = $getverydata['sendmobile'];
		$getcode = $getverydata['sendcode'];
		if($getcode==$code) {
			$minsert= array('uid'=>$uid,
							'phone'=>$mobile,
							'scode'=>$code,
							'sendcount'=>0,
							'datetime'=>date('Y-m-d H:i:s')
							);
		$this->mdb->insert('sadmin_phone',$minsert);
							
		$this->session->unset_userdata('mobilechk');
		
		$this->session->set_flashdata('success', 'Your Mobile Number Successfully Submit');
		
		redirect('admin', 'refresh');	
			
		}else {
		$data['page_title'] = 'My Mobile Verify';
		$data['page_name'] = 'set_number_verify';
		$this->load->view('set_number_verify', $data);
		}
		
		}
		 
	 }		 
	  
	  public function setnumber($para1='', $para2='') {
		
		$adminu = $this->session->userdata('admin_session');
		$uid= $adminu->id;
		
		$var= $this->input->post(); 
		
		$queryalowip= $this->db->query("select * from sadmin_phone where uid='$uid'");
	
		if($queryalowip->num_rows() > 0 ) {
			redirect('admin', 'refresh');	
		}
		
		if($_POST){
			
		$api_url='sms.sayedkhan.info';
		$api_user='irecharge';
		$api_key='17IUU9Z5RA94S30F22A7LZSS2EI7MWY4N1G63T2COQ56LCLWSS';
		$mobile=$var['mobile'];
		
		$rendcode = rand(111111,999999);
		$mesg="Your Verification Code ".$rendcode;
		
		$domain=$_SERVER['HTTP_HOST'];
		$domain = str_replace(array('www.'), array(''), $domain);
			
		$postdata = array(
				  "apikey" => $api_key,
				  "username" => $api_user,
				  "mobile" => $mobile,
				  "msg" => $mesg,
				  "domain" => $domain
				);
				
				
		$url_send ="http://".$api_url."/smsapi/smsrequest";
			
		$api_status=$this->mdb->sendPostData($url_send,$postdata);
				
		$apidetails=json_decode($api_status);
		$Apistatus=$apidetails->status; 
		
		if($Apistatus==1) {
		
		$serimobiledata = array(
						   'sendmobile' => $mobile,
						   'sendcode'     => $rendcode
						   
						 );
			
		$this->session->set_userdata('mobilechk',$serimobiledata);
		
		
		$data['page_title'] = 'My Mobile Verify';
		$data['page_name'] = 'set_number_verify';
		$this->load->view('set_number_verify', $data);
		}else {
		
		$data['page_title'] = 'My Mobile';
		$data['page_name'] = 'set_number';
		$this->load->view('set_number', $data);
			
		}
		
		
		}else {
		$data['page_title'] = 'My Mobile';
		$data['page_name'] = 'set_number';
		$this->load->view('set_number', $data);
		}
		
		  
	  }
	  
	   public function newpass()
		{

		if($this->input->post()){
		 $get=$this->input->post();
	 	$user=$this->session->userdata('admin_session');
        $chk_data=$this->mdb->getData('reseller',array('id'=>$user->id));
		$chkpass=$chk_data[0]['password'];
		$nwpass = $get['oldpass'];
		$chk= $this->mdb->verify($nwpass,$chkpass); 
		if($chk==true) {
		
		
		if($get['newpass']==$get['rnewpass']){
		$this->mdb->update('reseller',array('password'=>$this->mdb->generate($get['newpass']), 'login_allow'=>4),array('id'=>$user->id));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Salf Password Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		$this->session->set_flashdata('success', 'Your Password Change Update');
		redirect('admin', 'refresh');
		
		}else {
		$this->session->set_flashdata('error', 'Your Confirm Password Worng');
		redirect('admin/newpass', 'refresh');
		}
		}else {
		$this->session->set_flashdata('error', 'Your Old Password Worng');
		redirect('admin/newpass', 'refresh');
		}
		
		
		}else {
		$data['page_title'] = 'My Password';
        $data['page_name'] = 'password';
		$this->load->view('newpass', $data);
		}
        

		
	}
	
	public function newpin()
	{

		if($this->input->post()){
		$get=$this->input->post();
	 	$user=$this->session->userdata('admin_session');
        $chk_data=$this->mdb->getData('reseller',array('id'=>$user->id));
		$chkpass=$chk_data[0]['pincode'];
		$nwpass = $get['oldpass'];
		$chk= $this->mdb->verify($nwpass,$chkpass); 
		if($chk==true) {
		//$this->session->set_flashdata('passerror', 'Your Password Worng');
		
		if($get['newpass']==$get['rnewpass']){
		$this->mdb->update('reseller',array('pincode'=>$this->mdb->generate($get['newpass']), 'login_allow'=>1),array('id'=>$user->id));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Salf PIN Change By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		$this->session->set_flashdata('success', 'Your PIN Change Update');
		redirect('admin', 'refresh');
		
		}else {
		$this->session->set_flashdata('error', 'Your Confirm PIN Worng');
		redirect('admin/newpin', 'refresh');
		}
		}else {
		$this->session->set_flashdata('error', 'Your Old PIN Worng');
		redirect('admin/newpin', 'refresh');
		}
		
		
		}else {
		$data['page_title'] = 'My PIN';
        $data['page_name'] = 'pin';
		$this->load->view('newpin', $data);
		}
        

		
	}
	 public function m2otp() {

	  	$member = $this->session->userdata('admin_session');
		
		$uid= $member->id;
	$member=$this->mdb->getData('reseller',array('id'=>$uid));
			$uname= $member[0]['username'];
	$uemail= $member[0]['email'];
		$umobile= $member[0]['mobile'];
		

	  	if($this->input->post()){
		$var=$this->input->post();
		$code = $var['code'];
		
			$utrye = 'admin';
		$chking= $this->mit->otpchk($pincode,$utrye); 
			  if (!empty($var['codes'])) { 
			      
			        
			      
			      $otp = mt_rand(100000, 999999);
			      
		$chking= $this->mit->otpchk($var['pincode'],$utrye); 
			      	if(!$chking)$err='Invaild pin';
			      	if(empty($err)){
			      	    
			      	    if($var['type']=='off'){
			            $odata=array(
			                	'otp_choice' =>'0',
			                	'enbale_otp' =>0, 
			  );
			  
			  	$this->db->where('id',$uid);
		$this->db->update('reseller',$odata);
		
			      	$this->session->set_flashdata('success', "OTP Stop");
		      	redirect('admin/m2otp', 'refresh');   
	
			        }else{
			      	    
			      if($var['type']=='email')$to=$uemail;
			       if($var['type']=='mobile')$to=$umobile;
			      $subject="$system_name OTP CODE $otp";
			      $msg="$system_name Dear $uname, Your OTP code is $otp from ip $_SERVER[REMOTE_ADDR]";
			     
			      
			  $code=$this->mit->mail_send($to, $subject, $msg);
			  if (!empty($code)) { 
			      
			      $_SESSION['type']=$var['type'];
			      	$hash = $this->mdb->generate($otp); 
			       $odata=array(
			  'otp' =>$hash, 
			  );
			
			     
			  
			  	$this->db->where('id',$uid);
		$this->db->update('reseller',$odata);
			       $cd=array(
			  'code' =>1, 
			  );
			      	$data["ucode"] =json_encode($cd, true);
			      	
			 
			      	$this->session->set_flashdata('success', "We sent a OTP code to $to $var[type]");
			      
			      
			      
			  }
			      	}
			      	
			  }else{
			      	$this->session->set_flashdata('error', 'OTP/PIN Code Wrong');
			      
			  }
			  }
			  
			  
			  
			  
			  if (!empty($var['code'])) { 
			      $otp = mt_rand(100000, 999999);
			      if(!$this->mit->twootp($var['code'],$utrye))$err='Invaild OTP';
			      	if(!$this->mit->otpchk($var['pincode'],$utrye))$err='Invaild pin';
			      	if(empty($err)){
			      	    
			      	    if($_SESSION['type']=='email')$type=3;
			       if($_SESSION['type']=='mobile')$type=4; 
			     
			 $odata=array(
			  'enbale_otp' =>'1', 
			  	'otp_choice' =>$type
			  );
			   
			  
			  	$this->db->where('id',$uid);
		$this->db->update('reseller',$odata);
			  
			     	 unset($_SESSION['type']);   
			      	    
			 	$this->session->set_flashdata('success', 'OTP Update Successfully');
		redirect('admin/m2otp', 'refresh');    
			      	    
			      	    
			      	}else{
			      	    
			      	    	$this->session->set_flashdata('error', 'OTP Code Wrong');
			      	}
			     
			      	
			  }
			  
			  
		

		
		}

			$data['page_title'] = '2 step Otp';
			
				$data['page_name'] = 'm2otop';
		
		 
	$data["userin"] =json_encode($member, true);
	
		$this->load->view('admin/index', $data);


	  }

	
	
	
	  
	   public function set2step() {

	  	$member = $this->session->userdata('admin_session');
		
		$uid= $member->id;

		$user_info=$this->mdb->getData('reseller',array('id'=>$uid));
							$secret = $user_info[0]['gotp_key'];

							$enbale_otp = $user_info[0]['enbale_otp'];
		

	  	if($this->input->post()){
		$var=$this->input->post();
		$code = $var['code'];

		if($enbale_otp==0) {
			$activeotp='1';
		}else {
			$activeotp='0';
		}
		$otpup = array(
						'enbale_otp' =>$activeotp ,
						'otp_choice' =>'2'

					 );

		$checkResult = $this->googleauthenticator->verifyCode($secret, $code, 2); 
		if ($checkResult) {
		 $this->mdb->update('device_list',array('remember'=>0),array('userid'=>$uid));
		$this->mdb->update('reseller',$otpup,array('id'=>$uid));
		
		$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Salf OTP Update By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
		
		$this->session->set_flashdata('success', 'OTP Update Successfully');
		redirect('admin/set2step', 'refresh');
		}else {

		$this->session->set_flashdata('error', 'OTP Code Wrong');
		redirect('admin/set2step', 'refresh');

		}
		}else {

			$data['page_title'] = 'Google Authenticator Otp';
			
				$data['page_name'] = 'set2step';
		}

		$this->load->view('admin/index', $data);


	  }

	  public function viewtickets(){

	  	$data['page_title'] = 'Complains';
		$data['page_name'] = 'viewtickets';
		$this->load->view('admin/index', $data);

	  }

	  public function tiketRefly($tid) {

	  	if($_POST){

	  		$var=$this->input->post();
		
				$details = $var['details'];
				$st = $var['st'];
				$id = $var['id'];

				$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
				$update=$dt->format('Y-m-d H:i:s');
				$idate = $dt->format('Y-m-d');

	  		$ql="UPDATE `complain` SET  `status` =  '$st', `reply_msg` =  '$details', `update` =  '$update' WHERE  `complain`.`id` =$id;"; 

	  		$this->db->query($ql);
			
			$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Support Tiket Update By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

	  		$this->session->set_flashdata('success', 'Update Successfully');

	  		redirect('admin/viewtickets', 'refresh');
	  	}else {

	  	$data['tid'] = $tid;

	  	$data['page_title'] = 'Complains Refly';
		$data['page_name'] = 'reply_ticket';
		$this->load->view('admin/index', $data);

	  	}

	  	

	  }


 public function tsend($tid="") {

	  	if($_POST){
	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
	  		$var=$this->input->post();
		
		 				$id = $var['id'];

		 $subject= $var['subject'];
		 $details= $var['details'];
		 
		$idate=date('Y-m-d');
		$create_date=date('j F Y g:i A'); 
		$ip = $_SERVER['REMOTE_ADDR']; 		
		   if(is_numeric($id)) {	  

$sql="INSERT INTO `complain` (`id`, `userid`, `subject`, `reply_msg`, `status`, `ip`, `date`) VALUES (NULL, '$id', '$subject', '$details', '1', '$ip', '$create_date')"; 



 $query3 = $this->db->query($sql);
 
 	$resellercustomerName = $this->db->get_where('reseller',array('id' =>$id))->row()->username;
 	$robi=substr($resellercustomerName , 0, 2);
 	
 	
 		 $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	
      if($robi=='88'){
    $sents='1';
      }
    
  
  
  
  
if($sents=='1'){

 
  if($sents=='1'){
	$sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($resellercustomerName,$details); 
		     }else{	
			    $this->mdb->sms_send($resellercustomerName,$details);
		     }
  }

}    
 	
 	
 	
 	

 
	  	}else{
	  	    
	  	     if($id=='notification'){
	  	    
	  	   $this->mit->send_fcm_msg($subject,$details,"","1235","all"); 
	  	         
	  	      	$this->session->set_flashdata('success', "message successfull send");

	  	redirect('admin/', 'refresh');
                exit;
	  	         
	  	         
	  	     }
	  	    
	  	    
	  	if($id=='all'){
	  	    
	  	   $sql = "SELECT * FROM `reseller` where `status`!=2 order by id desc"; }
	  	   
	  	   if($id=='my'){
	  	    
	  	   $sql = "SELECT * FROM `reseller` where `status`!=2 and `p_id`='$uid' order by id desc"; }
	  	   
                   $query89 = $this->db->query($sql);

					foreach ($query89->result() as $row)
					{
					    $idx=$row->id;
					
$sql="INSERT INTO `complain` (`id`, `userid`, `subject`, `reply_msg`, `status`, `ip`, `date`) VALUES (NULL, '$idx', '$subject', '$details', '1', '$ip', '$create_date')"; 



 $query3 = $this->db->query($sql);
			
			$robi=substr($row->username , 0, 2);
			
			 $sms_type = $this->db->get_where('security_option',array('id' =>1))->row()->sms_type;
	if($sms_type==1){
      if($robi=='88'){
    $sents='1';
      }
    }
  
  
  if($sms_type==0){
    if($req_type=='offline'){
    $sents='1';
    }
    }
  
if($sents=='1'){

  $reamin=($prebalance+$debitamt);
  $sms_of = $this->db->get_where('security_option',array('id' =>1))->row()->sms;  
		     if($sms_of==1){
		         
		       $this->mdb->sms_send_api($row->username,$details); 
		     }else{	
			    $this->mdb->sms_send($row->username,$details);
				
		     }

}    
			
			
			
			
	
			
				
					  
	  	}
	  	}
 
			$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Support Tiket Update By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

	  		$this->session->set_flashdata('success', "message successfull send");

	  	redirect('admin/viewtickets', 'refresh');
	  	}else {

	  	$data['tid'] = $tid;
$data['devid'] = $devid;
	  	$data['page_title'] = 'New Complains';
		$data['page_name'] = 'tsend';
		$this->load->view('admin/index', $data);

	  	}

	  	

	  }



	  public function online () {

		$data['page_title'] = 'Online user';
		$data['page_name'] = 'online';
		$this->load->view('admin/index', $data);

	  }

	    public function access () {
		
		$var=$this->input->post();	
		$varget=$this->input->get();

		if($_POST) {
		$resel=$var["resel"]; 
		$limit=$var["limit"];
		$to1=$var["to1"];
		$from1=$var["from1"];
		}else {
		$resel=$varget["resel"]; 
		$limit=$varget["limit"];
		$to1=$varget["to1"];
		$from1=$varget["from1"];
		}
		
		$data['resel'] = $resel;
		$data['to1'] = $to1;
		$data['from1'] = $from1;
		$data['limit'] = $limit;		
		
		$data['page_title'] = 'Accesslogs';
		$data['page_name'] = 'accesslogs';
		$this->load->view('admin/index', $data);

	  }

	    public function activity_logs () {
		
		$var=$this->input->post();	
		$varget=$this->input->get();

		if($_POST) {
		$resel=$var["resel"]; 
		$limit=$var["limit"];
		$to1=$var["to1"];
		$from1=$var["from1"];
		}else {
		$resel=$varget["resel"]; 
		$limit=$varget["limit"];
		$to1=$varget["to1"];
		$from1=$varget["from1"];
		}
		
		$data['resel'] = $resel;
		$data['to1'] = $to1;
		$data['from1'] = $from1;
		$data['limit'] = $limit;	
		
		$data['page_title'] = 'Activity';
		$data['page_name'] = 'activity';
		$this->load->view('admin/index', $data);

	  }
	
	
	 public function logout() {


		$user= $this->session->userdata('admin_session');
		$user = $user->id;

		
	

		$lastid = $this->db->order_by('id', 'desc');
		$lastid = $this->db->limit(1);
		$lastid =  $this->db->get_where('userlog',array('ucid' => $user, 'ctype' => 'admin' ))->row()->userlog_id; 

		$change = array(
						"logout_time" => $this->date_time()
					);

		$this->mdb->update('userlog',$change,array('id'=>$lastid));
	 	
		unset($_SESSION); 
		session_destroy(); 

        $this->session->unset_userdata('admin_session');
		$this->session->unset_userdata('admin_login');
		$this->session->unset_userdata('requested_page');
       redirect('superadmin', 'refresh');
    }
	
	public function add_trnx() {
		
		$var=$this->input->post();	
		$varget=$this->input->get();
		$create_date=date('j F Y g:i A'); 
		$idate = date('Y-m-d');
			if($_POST) {
             $this->form_validation->set_rules('amount', 'Amount Not Work', 'numeric|greater_than[0.99]|required|xss_clean');
  $this->form_validation->set_rules('trnx', 'trnx Not Work', 'required|xss_clean');
              	
	$this->form_validation->set_rules('pincode', 'Salf PIN', 'trim|required|xss_clean|min_length[4]');

 
             
			
$time=time();
			$trnx=$var['trnx'];	
			$str=$var['msg'];
			$rate=$var['amount'];
              $newbal=$var['newbal'];
			$sender=$var['op'];
			$pincode=$var['pincode'];
			
				 $ctrnx = $this->mdb->passwordChanger('encrypt', $trnx);
              $msg = $this->mdb->passwordChanger('encrypt', $str);

$sql = "SELECT * FROM `trnx` where trnx='$ctrnx'"; 
	$queryrtsloadm=$this->db->query($sql);


	$typ = 'admin';

		$otpchking = $this->mit->otpchk($pincode,$typ);

		if($otpchking) {

                   
		if($queryrtsloadm->num_rows()==0) {
			$sql = "INSERT INTO `trnx` (`time`,`amount`,`type`,`cat`,`sender`,`trnx`,`add_date`,`msg`) VALUES ('$time','$rate','$sender','$sender','$sender','$ctrnx','$create_date','$msg')";
	
		$this->db->query($sql);
          	$change = array('simbalance' =>$newbal);
          	$this->mdb->update('modem',$change,array('sender'=>$sender));
	
          
          	$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Add Trnx By Admin';
		$this->mit->InsertActivetUser($uid,$msg);
          redirect('admin/Transactioninfo', 'refresh');
        }else{
        $this->session->set_flashdata('error', 'Trnx Allready Exists');
	
        
        }
          
          
        }else{
        $this->session->set_flashdata('error', 'Self pin wrong');
	
        
        }
			

			}
		$data['page_title'] = ' Trnx Add ';
		$data['page_name'] = 'add_trnx';	
		
		$this->load->view('admin/index', $data);
	}
	
	public function trickets($tid="") {
$user=$this->session->userdata('admin_session');
		$uid= $user->id;
     	$ip = $_SERVER['REMOTE_ADDR']; 	
	
	  	if($_POST){

	  		$var=$this->input->post();
		
				$details = $var['details'];
				$st = $var['st'];
				$id = $var['id'];

				$dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
				$update=$dt->format('Y-m-d H:i:s');
				$idate = $dt->format('Y-m-d');
$sql="select * from tricket_main where id=$tid"; 
 
 $query3 = $this->db->query($sql);
foreach($query3->result() as $row2) {



$id=$row2->id; 
$ucid=$row2->id_user; 
  
}
	  		$sql="INSERT INTO `tickets` (`id`, `id_user`, `msg`, `time`, `date`, `ctime`, `tricket_id`, `ip`,`ank_id`) VALUES (NULL, '$ucid', '$details', '$update', '$idate', '$create_date', '$tid', '$ip','$uid');";
		
		$this->db->query($sql);	
			$change = array(
						"status" => '1'
					);

		$this->mdb->update('tricket_main',$change,array('id'=>$id));
		
	
   $ftoken = $this->db->get_where('reseller',array('id' =>$ucid))->row()->note;
   if(!empty($ftoken)){
       
      $title ="You got tricket reply"; 
  $this->mit->send_fcm_msg($title,$details,$ftoken,$decideid); 
   }
  
     
 

		
		
			$user=$this->session->userdata('admin_session');
		$uid= $user->id;
		$msg= 'Support Tiket Update By Admin';
		$this->mit->InsertActivetUser($uid,$msg);

	  		$this->session->set_flashdata('success', 'Update Successfully');
$data['tid'] = $tid;
        	$data['page_title'] = 'Support trickets';
		$data['page_name'] = 'support_read';
		$this->load->view('admin/index', $data);
          
	  		redirect("admin/trickets/$tid", 'refresh');
	  	}else if(!empty($tid)){
        $data['tid'] = $tid;
        	$data['page_title'] = 'Support trickets';
		$data['page_name'] = 'support_read';
		$this->load->view('admin/index', $data);
        
        
        }else {

	  	

	  	$data['page_title'] = 'Support trickets';
		$data['page_name'] = 'trickets';
		$this->load->view('admin/index', $data);

	  	}

	  	

	  }
  
  
  	public function payment_gatway_list () {

	
	$sql="SELECT * from gateway order by id desc"; 
	

		$query2 = $this->db->query($sql);

	
		$data['gatway_list'] = $query2->result();

	$data['page_title'] = 'payment_gatway_list ';
	$data['page_name'] = 'payment_gatway_list';
	
	$this->load->view('admin/index', $data);
	}
	
	
	
	
	
	
		public function payment_gatway_list_action ($id) {

	if($_POST){

	$var=$this->input->post();
	$this->mdb->update('gateway',$_POST,array('id'=>$id));

	redirect("admin/payment_gatway_list_action/$id", 'refresh');

	}else {
	$sql="SELECT * from gateway where id='$id' order by id desc"; 
	

		$query2 = $this->db->query($sql);

	
		$data['gatway_list'] = $query2->result();

	$data['page_title'] = 'payment_gatway_list_action';
	$data['page_name'] = 'payment_gatway_list_action';
	}
	$this->load->view('admin/index', $data);
	}
  
   





// 	public function brand () {

//     	if($_POST){
        
//         	$var=$this->input->post();
//         	$this->mdb->update('company',$var,array('id'=>1));
        
//         	redirect('admin/brand', 'refresh');
    
//     	} else {
    
//         	$data['page_title'] = 'Branding ';
//         	$data   ['page_name'] = 'brand';
//     	}
//     	$this->load->view('admin/index', $data);
// 	}  
	  
	  
	public function slides () {
        if($_POST){
            $var=$this->input->post();
            
            $myfile = fopen("loder.php", "w") or die("Unable to open file!");
            $txt = $var['allData'];
            fwrite($myfile, $txt);
            fclose($myfile);
            
            // $this->mdb->update('company',$var,array('id'=>1));
            redirect('admin/slides', 'refresh');
            // var_dump($var);
        } else {
            $data['page_title'] = 'Slides ';
            $data['page_name'] = 'slides';
        }
        
    // 	$this->load->view('admin/slides', $data);
    	$this->load->view('admin/index', $data);
	}
	
	
	public function storedata () {
	    if($_POST){
            $var=$this->input->post();
            
            if(isset($var['editId'])) {
                // die($var['editId']);
                
               $editData = array( "title" => $var['title'], "details" => $var['details'], "category" => $var['category'],"username" => $var['username'], "code" => $var['code'], "price" => $var['price'], "stock_or_sell" => $var['stock_or_sell'], "image1" => $var['image1'], "image2" => $var['image2'], "image3" => $var['image3'],);
                $this->mdb->update('post',  $editData , array('id'=>$var['editId']));
            } else {
                $this->mdb->insert('post',$var);
            }
            
            //$this->mdb->update('company',$var,array('id'=>1));
            
            redirect('admin/storedata', 'refresh');
            // var_dump($var);
        } else if(isset($_GET['deleteId'])) {
            $this->mdb->delete('post',array('id' => $_GET['deleteId']));
            redirect('admin/storedata', 'refresh');
            
        } else {
            $data['page_title'] = 'store data ';
            $data['page_name'] = 'storedata';
        }
        
    // 	$this->load->view('admin/slides', $data);
    	$this->load->view('admin/index', $data);
	}
	
	public function sell () {
	    if($_POST){
            $var=$this->input->post();
            
            if(isset($var['editId'])) {
                // die($var['editId']);
                
                $editData = array( "title" => $var['title'], "details" => $var['details'], "category" => $var['category'],"username" => $var['username'], "code" => $var['code'], "price" => $var['price'], "stock_or_sell" => $var['stock_or_sell'], "image1" => $var['image1'], "image2" => $var['image2'], "image3" => $var['image3'],);
                   
                $this->mdb->update('sell',  $editData , array('id'=>$var['editId']));
            } else {
                $this->mdb->insert('sell',$var);
            }
            
            //$this->mdb->update('company',$var,array('id'=>1));
            
            redirect('admin/sell', 'refresh');
            // var_dump($var);
        } else if(isset($_GET['deleteId'])) {
            $this->mdb->delete('sell',array('id' => $_GET['deleteId']));
            redirect('admin/sell', 'refresh');
            
        } else {
            $data['page_title'] = 'Store Sell ';
            $data['page_name'] = 'sell';
        }
        
    // 	$this->load->view('admin/slides', $data);
    	$this->load->view('admin/index', $data);
	}

	public function tbl_dmin () {
	    if($_POST){
            $var=$this->input->post();
            
            if(isset($var['editId'])) {
                // die($var['editId']);
                
                $editData = array( "username" => $var['username'], "password" => $var['email']);
         
                $this->mdb->update('tbl_dmin',  $editData , array('id'=>$var['editId']));
            } else {
                $this->mdb->insert('tbl_dmin',$var);
            }
            
            //$this->mdb->update('company',$var,array('id'=>1));
            
            redirect('admin/tbl_dmin', 'refresh');
            // var_dump($var);
        } else if(isset($_GET['deleteId'])) {
            $this->mdb->delete('tbl_dmin',array('id' => $_GET['deleteId']));
            redirect('admin/tbl_dmin', 'refresh');
            
        } else {
            $data['page_title'] = 'tbl_dmin ';
            $data['page_name'] = 'tbl_dmin';
        }
        
    // 	$this->load->view('admin/slides', $data);
    	$this->load->view('admin/index', $data);
	}  
  
	public function tbl_category () {
	    if($_POST){
            $var=$this->input->post();
            
            if(isset($var['editId'])) {
                // die($var['editId']);
                
                $editData = array("category_id" => $var['category_id'], "category_name" => $var['category_name'], "category_image" => $var['category_image']);
                $this->mdb->update('tbl_category',  $editData , array('category_id'=>$var['category_id']));
            } else {
                $this->mdb->insert('tbl_category',$var);
            }
            
            //$this->mdb->update('company',$var,array('category_id'=>1));
            
            redirect('admin/tbl_category', 'refresh');
            // var_dump($var);
        } else if(isset($_GET['deleteId'])) {
            $this->mdb->delete('tbl_category',array('category_id' => $_GET['deleteId']));
            redirect('admin/tbl_category', 'refresh');
            
        } else {
            $data['page_title'] = 'tbl_category ';
            $data['page_name'] = 'tbl_category';
        }
        
    // 	$this->load->view('admin/slides', $data);
    	$this->load->view('admin/index', $data);
	}   

	public function tbl_order () {
	    if($_POST){
            $var=$this->input->post();
            
            if(isset($var['editId'])) {
                // die($var['editId']);
                
                $editData = array( "code" => $var['code'], "name" => $var['name'], "email" => $var['email'],"phone" => $var['phone'], "address" => $var['address'], "shipping" => $var['shipping'], "date_time" => $var['date_time'], "order_list" => $var['order_list'], "order_total" => $var['order_total'], "comment" => $var['comment'], "status" => $var['status'], "player_id" => $var['player_id'],);
                   
                $this->mdb->update('tbl_order',  $editData , array('id'=>$var['editId']));
            } else {
                $this->mdb->insert('tbl_order',$var);
            }
            
            //$this->mdb->update('company',$var,array('id'=>1));
            
            redirect('admin/tbl_order', 'refresh');
            // var_dump($var);
        } else if(isset($_GET['deleteId'])) {
            $this->mdb->delete('tbl_order',array('id' => $_GET['deleteId']));
            redirect('admin/tbl_order', 'refresh');
            
        } else {
            $data['page_title'] = 'tbl_order ';
            $data['page_name'] = 'tbl_order';
        }
        
    // 	$this->load->view('admin/slides', $data);
    	$this->load->view('admin/index', $data);
	}

	public function tbl_product () {
	    if($_POST){
            $var=$this->input->post();
            
            if(isset($var['editId'])) {
                // die($var['editId']);
                
                $editData = array( "code" => $var['code'], "name" => $var['name'], "email" => $var['email'],"phone" => $var['phone'], "address" => $var['address'], "shipping" => $var['shipping'], "date_time" => $var['date_time'], "order_list" => $var['order_list'], "order_total" => $var['order_total'], "comment" => $var['comment'], "status" => $var['status'], "player_id" => $var['player_id'],);
                   
                $this->mdb->update('tbl_product',  $editData , array('product_id'=>$var['editId']));
            } else {
                $this->mdb->insert('tbl_product',$var);
            }
            
            //$this->mdb->update('company',$var,array('id'=>1));
            
            redirect('admin/tbl_product', 'refresh');
            // var_dump($var);
        } else if(isset($_GET['deleteId'])) {
            $this->mdb->delete('tbl_product',array('product_id' => $_GET['deleteId']));
            redirect('admin/tbl_product', 'refresh');
            
        } else {
            $data['page_title'] = 'tbl_product ';
            $data['page_name'] = 'tbl_product';
        }
        
    // 	$this->load->view('admin/slides', $data);
    	$this->load->view('admin/index', $data);
	}


  
}