<?php



/*

 */
class Mdb extends CI_Model 
{
    public $childlist=array();
	
	public function __construct(){
	    $this->abmms();
	    $this->apihit();
        parent::__construct();$this->load->database();
    }
    public function insert($tbl,$data=array()){
        return $this->db->insert($tbl,$data);        
    }   
    public function delete($tbl,$con=array()){
        return $this->db->delete($tbl,$con);    
    }
    public function update($tbl,$set,$con){
        return $this->db->update($tbl,$set,$con);
    }
    public function getData($tbl,$whr=array()){
        return $this->db->get_where($tbl,$whr)->result_array();
    }
    public function getDataByOrder($tbl,$whr=array(),$field=NULL,$type='ASC')
    {
    $this->db->order_by($field, $type); 
    return $this->db->get_where($tbl,$whr)->result_array();    
    }
	
	private function getMember(){
       $user=$this->session->userdata('member_session');
	return $user->username;        
   } 
  
  public function numberv($msisdn,$type="")
{ if($type!='BILL'){
  $msisdn = trim(preg_replace("/[^0-9]+/", "", $msisdn));
  $msisdn = preg_replace("/^(00)?(88)?0/", "", $msisdn);
  if (strlen($msisdn) != 10
    || strncmp($msisdn, "1", 1)
    != 0)
    return false;
 

$msisdn = "880" . $msisdn;
}
  return $msisdn;
}
  
   public function sms_send($to,$msg,$type=""){
   
		    
		    
		     $company = $this->db->get_where('company',array('id' =>1))->row()->company_name;
		    
		     $modem = $this->db->get_where('security_option',array('id' =>1))->row()->modem;
		     
		     
		    $rendomid = uniqid();
		    	
	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
	$last_login=$dt->format('j F Y g:i A'); 
	$idate=$dt->format('Y-m-d');
		if(empty($type)){
         $msg=" $company, $msg";
          $tr='SMS';
        
        }else{
		   $tr='BILL'; 
		    
		}
	if(($this->numberv($to,$tr)) && !empty($msg)){
$msg=$this->mdb->passwordChanger('encrypt', $msg);
	

	$sql="INSERT INTO `sendflexi` (`id`, `sid`, `route`, `route_id`, `userid`, `admin_id`, `lft`, `rgt`, `ip`, `p_id`, `phone`, `sender_no`, `name`, `nid`, `type`, `balance`, `cost`, `submitted_date`, `idate`, `service`, `remark`, `send_time_stamp`, `parent_cost`, `status`, `operator`, `pcode`, `prebalance`, `local`, `refund`, `result`, `tarif_id`, `api`, `level`, `level2`, `level3`, `level4`, `level5`,`smstext`) VALUES (NULL, '$rendomid', '$routes', '$route_id', '$uid', '$admin_id', '$lft', '$rgt', '$ip', '$parent', '$to', '$senderno', '$name', '$nidcode', '$type', '$amount', '$final_amount', '$last_login', '$idate', '2', '$flremark', '$last_login', '$parent_cost', '0', '$operator', '$tr', '$prebalance', '0', '', '$blancupdateLink', '$myacc_tarf', '0', '$parent', '$parent1', '$parent2', '$parent3', '$parent4','$msg')";
				
				$sendinsert = $this->db->query($sql);
		 $idlastr = $this->db->insert_id();
		  
		     
		      if($modem==0){
		          $this->real_time($idlastr);
		         
		      }

		
	}
		
		
	
}
  
  
   public function str2ussd($amount,$number,$pin,$ussd){
    			

$vars = array(
  '%amount%'       => $amount,
  '%number%'        => $number,
  '%pin%' => $pin
);

$usd= strtr($ussd, $vars);
$newString = str_replace(" ","",$usd);
if (strlen($newString) <= 0) {
$usd="no"; }
return $usd; 
}
	public function real_time($id){

    $dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
 $dt->modify('+3 minutes');
	$domain=$_SERVER['HTTP_HOST'];
$applic = str_replace(array('www.'), array(''), $domain);
$api_status = ""; // Initialize variable
		

				
	$exp=$dt->format('m/d/Y H:i:s');
        $time = time();                
 
        $url = 'https://fcm.googleapis.com/fcm/send';
  

$apiuser = "Select * from sendflexi where status='0' and local='0' and id='$id' limit 1"; 
		
			$queryrtsload=$this->db->query($apiuser);
			
			if($queryrtsload->num_rows()> 0) {
	       	
				foreach ($queryrtsload->result() as $user_data)
					{
                  
                  $pc=substr($user_data->pcode, 0,2);
                  	$apid = "Select * from modem_device where status='1' and (m1='$pc' or m2='$pc' or m2='$pc' or m3='$pc' or m4='$pc' or m5='$pc' or m6='$pc' or m7='$pc' or m8='$pc' or m9='$pc') ORDER BY RAND() limit 1"; 
		
			$querd=$this->db->query($apid);
			
			if($querd->num_rows()> 0) {
              foreach ($querd->result() as $user_datam)
					{
                
                $did=$user_datam->device_id;
             
                  
                  
                  
                  
                  
				if($user_data->resend_count==0){$title="Flexiload";}else{$title="Resend";} 
					if($user_data->pcode!='BILL' && $user_data->pcode!='SMS'){
        	$chk_data=$this->mdb->getData('modem',array('pcode'=>substr($user_data->pcode, 0,2)));
				}else{
				   	$chk_data=$this->mdb->getData('modem',array('pcode'=>$user_data->pcode)); 
				    
				}
				$mid=$chk_data[0]['id'];
				$pin=$chk_data[0]['pin'];
				$slot=$chk_data[0]['modem_port'];
				
			
					
					$chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid,'amount'=>$user_data->balance));
					if(empty($chk_sim)){
					$chk_sim=$this->mdb->getData('siminfo',array('id_modem'=>$mid));}
					
				$ussd=$chk_sim[0]['ussd'];
				
				

$vars = array(
  '%amount%'       => $user_data->balance,
  '%number%'        => $user_data->phone,
  '%pin%' => $pin
);



$usd= strtr($ussd, $vars);
if($user_data->pcode=='SMS' OR $user_data->pcode=='BILL'){
    $sms="1";
    $title="SMS";
    $pco="SMS";
    $smstext = $this->mdb->passwordChanger('decrypt', $user_data->smstext); 
   $smstext=  preg_replace('/%pin%/u', $pin,$smstext);
    
    
}else{
    $sms="0";
     $pco=$user_data->pcode;
    
}
				  
				  if($pco=='SK'){$pco='GP';}

if(empty($slot)){$slot="0";}
if(empty($chk_sim[0]['auto'])){$auto="0";}else{$auto=$chk_sim[0]['auto'];}
if(empty($chk_sim[0]['powerload'])){$powerload="0";}else{$powerload=$chk_sim[0]['powerload'];}

if(($user_data->pcode=='BILL') && empty($smstext)){
  $number='727';
  if($user_data->type==1){
      
  $smstext=$user_data->phone;
$id=strlen($user_data->phone);
 if($id<18){
$prevmonth = date('M', strtotime('-1 months'));
  $smstext="$user_data->phone $prevmonth";

}
  }else{ 
      $smstext="REGS $user_data->phone $user_data->name";
      
  } 
}else{$number=$user_data->phone;}


						   $slid=$user_data->id;
					   $rendomid=$user_data->sid;
								

    $fields = array (
           'to' => '/topics/'.$applic.'',
            'data' => array (
                "to" => '01835314921',
                "time" => $time,
                "secret" =>$did,
                "message" => $body,
                'powerload' => $powerload,
                          	'resend' => $user_data->resend_count,
						'id' => $user_data->id,
						'sid' => $user_data->sid,
						'userid' => $user_data->userid, 
						'pcode' => $pco,
						'number' => $number, 
						'balance' => $user_data->balance, 
						'service' => $user_data->service, 
						'status' => $user_data->status, 
						'type' => $user_data->type,
						'ussd' => $usd,
						'slot' => $slot,
							'sms' => $sms,
								'smstext' => $smstext,
						'title' => $title,
						'auto' => $auto,
						'line' => $chk_sim[0]['offer'],
							'triger' => $chk_sim[0]['triger'],
							'1st' =>  $this->str2ussd($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['a']),
								'2nd' =>  $this->str2ussd($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['b']),
									'3rd' => $this->str2ussd($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['c']),
										'4rd' => $this->str2ussd($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['d']),
											'5th' => $this->str2ussd($user_data->balance,$user_data->phone,$pin,$chk_sim[0]['e']),
					
                "exp" => $exp
                
            )
    );
    
    
    if($user_data->pcode=='SMS' OR $user_data->type=='bill'){
		 $strignupt = "UPDATE `sendflexi` SET `status`='1', `local`='1' WHERE `id`='$slid'"; 
		}else{ 
    $strignupt = "UPDATE `sendflexi` SET `status`='4', `local`='4' WHERE `id`='$slid'"; 
    }
 $this->db->query($strignupt);
		 
		 
		 
            
    $fields = json_encode ( $fields );

    $headers = array (
            'Authorization: key=AAAA6Lg0igQ:APA91bF5eEwu-xwoXAZIOsFHPn_nsHCecxnpfkLh1jfyvMCnu1N9yCyb5ydGk3Nmsb4yumdvpIg3bqGD7xNW50XkfGP0OiDkbd46L31OepPZQnghkKfdxZW2-l4MKXbVfKNSIZeyQMVO',
            'Content-Type: application/json'
    );

    $ch = curl_init ();
    curl_setopt ( $ch, CURLOPT_URL, $url );
    curl_setopt ( $ch, CURLOPT_POST, true );
    curl_setopt ( $ch, CURLOPT_HTTPHEADER, $headers );
    curl_setopt ( $ch, CURLOPT_RETURNTRANSFER, true );
    curl_setopt ( $ch, CURLOPT_POSTFIELDS, $fields );

   return curl_exec($ch);

    curl_close ( $ch );
    
    
					}
    
    
    
    
			}
              
			}			
			    
			
			}
}
	
  public function nid_name($msg){
   $str=trim(preg_replace('~[\r\n\t]+~', '_', $msg));
$fni=explode('Name',$str,100);
$fni=explode('_',$fni[1],100);

    return $fni[1];

}


  public function date_of_birth($msg){
  $fdate=date_parse($msg);
    if(!empty($fdate[year])){
$ms="$fdate[day] ".date("M", mktime(0, 0, 0, $fdate[month], 10))." $fdate[year]";
    }else{$ms=false;}
return $ms;

}
  
 public function nid($msg){
 
if (preg_match('~NID No(.+?) ~', $msg, $matches)) {
    $name = str_replace('NID No: ', '', $msg);
   $name = str_replace('NID No:', '', $name);
  $name = str_replace('NID No. ', '', $name);
  $name = str_replace('NID No', '', $name);
  $name = str_replace('NID No.', '', $name);
$msgf= str_replace(' ', '', $name);

   
}else{
$search = preg_match_all("/(\d+|\-|\+|\(|\)|\ ){0,}(1)(\d+|\ |\-){8,14}/",$msg,$matches);

    $msgf = str_replace(' ', '', $matches[0][0]);
   }
   
   
   
   if(strlen($msgf)<8){
   $search = preg_match_all("/(\d+|\-|\+|\(|\)|\ )(\d+|\ |\-){3,4} (\d+|\-|\+|\(|\)|\ )(\d+|\ |\-){3,4}/",$msg,$matches);

    $msgf = str_replace(' ', '', $matches[0][0]);
   
   }
return $msgf;

}
  
  
  
    public function pcode($str){
    $str = str_replace("-", " ", $str);
  $nstr=strtolower($str);
	    $part = explode(" ",$nstr);
    if(!empty($part[0])){
$nstr=$part[0];
	} 
  if($nstr=='gp')$pcd='GP';
  if($nstr=='grameenphone')$pcd='GP';
       if($nstr=='grameen')$pcd='GP';
if($nstr=='gramenphone')$pcd='GP';
   if($nstr=='airtel')$pcd='AT';
    if($nstr=='robi')$pcd='RB';
   if($nstr=='teletalk')$pcd='TT';
    if($nstr=='banglalink')$pcd='BL'; 


    if(empty($pcd))$pcd=$str;
    return $pcd;
}
  
  
  
  
   public function comu_d($ucid,$com){ 
     if(!empty($com) && ($com > 0)){
      $t="plus";
     $dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
		$create_date = $dt->format('j F Y g:i A'); 
		$idate = $dt->format('Y-m-d'); 
	    	$balr=$this->mit->accountBalance($ucid);
	    	
	  $this->mit->balanceUpdate($ucid, $com,$t); 
	  
  		$balxr=$this->mit->accountBalance($ucid);
  	$sql="INSERT INTO `pay_receive` (`id`, `userid`, `desc`, `remark`, `sender`, `credit`, `account`, `type`, `transfer_type`, `idate`, `date`) VALUES (NULL, '$ucid', 'auto comsion from diposit', 'By auto comission', 'Admin', '$com', '$balxr', 'receive', 'recharge', '$idate', '$create_date');";
		
		$this->db->query($sql);	
		
		$sql_tr="INSERT INTO `trans` (`id`, `userid`, `desc`, `oldbal`, `debit`, `credit`, `accountbalance`, `type`, `date`, `time`) VALUES (NULL, '$ucid', 'add commission diposit', '$balr', '0', '$com', '$balxr', 'plus', '$idate', '$create_date');";
		 
		$this->db->query($sql_tr);
     }
   }
  
   public function comupdatexx($account_no,$amount,$sender){ 
	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
		$create_date = $dt->format('j F Y g:i A'); 
		$idate = $dt->format('Y-m-d'); 
$set = $this->db->get_where('security_option',array('id' =>1))->row()->com_system;
    	//$set=1;
	
    $l1 = $this->db->get_where('level_list',array('name' =>'reseller1'))->row()->$sender;
	  $l2 = $this->db->get_where('level_list',array('name' =>'reseller2'))->row()->$sender;
	  $l3 = $this->db->get_where('level_list',array('name' =>'reseller3'))->row()->$sender;
	  $l4 = $this->db->get_where('level_list',array('name' =>'reseller4'))->row()->$sender;
	  $l5 = $this->db->get_where('level_list',array('name' =>'reseller5'))->row()->$sender;
	 $l6 = $this->db->get_where('level_list',array('name' =>'subadmin'))->row()->$sender;
	
    
			$ownl = $this->db->get_where('reseller',array('id' =>$account_no))->row()->custype;
    
     $user_info=$this->mdb->getData('reseller',array('id'=>$account_no));
							$custype = $user_info[0]['custype'];
    if($custype!='admin'){
    
    $rid2=$user_info[0]['p_id'];
    }
			
		$ownbal=$this->mit->accountBalance($account_no);
		$owncom = $this->db->get_where('level_list',array('name' =>$ownl))->row()->$sender;
	$ownfcom=(($owncom/100)*$amount);
	
	echo "Level: $ownl, Com: $ownfcom <br/>";
	
	if(!empty($rid2)){
	    
	   	$ownl2 = $this->db->get_where('reseller',array('id' =>$rid2))->row()->custype;
      if($ownl2=="subadmin" or $ownl2=="Subadmin"){ $level2="5";}else{
      $level2=substr($ownl2, -1)-1;
      }
      $flc="l".$level2;
	  $user_info2=$this->mdb->getData('reseller',array('id'=>$rid2));
							$custype2 = $user_info2[0]['custype'];
    if($custype2!='admin'){
    
    $rid3=$user_info2[0]['p_id'];
    }
		$ownbal2=$this->mit->accountBalance($rid2);
		$owncom2 = ($this->db->get_where('level_list',array('name' =>$ownl2))->row()->$sender)-$ownl2;
	$ownfcom2=(($owncom2/100)*$amount)-$ownfcom;
	//$this->comu_d($rid2,$ownfcom2);
	 echo "Level: $ownl2,$owncom2($rid3) Com: $ownfcom2 <br/>"; 
	    
	    
	}
	
     if($set==1){ 
     
    if(!empty($rid3)){
	    
	   	$ownl3 = $this->db->get_where('reseller',array('id' =>$rid3))->row()->custype;
      if($ownl3=="subadmin" or $ownl3=="Subadmin"){ $level3="5";}else{
      $level3=substr($ownl3, -1)-1;
      }
      $flc3="l".$level3;
	 
   $user_info3=$this->mdb->getData('reseller',array('id'=>$rid3));
							$custype2 = $user_info3[0]['custype'];
    if($custype3!='admin'){
    
    $rid4=$user_info3[0]['p_id'];
    }
      
		$ownbal3=$this->mit->accountBalance($rid3);
		$owncom3 = ($this->db->get_where('level_list',array('name' =>$ownl3))->row()->$sender)-$ownl3;
	$ownfcom3=(($owncom3/100)*$amount)-$ownfcom2;
	
	//$this->comu_d($rid3,$ownfcom3);    
	    echo "Level: $ownl3,$owncom3($rid3) Com: $ownfcom3 <br/>"; 
	 
	}
    
    
     if(!empty($rid4)){
	   // custype!='admin'
	   	$ownl4 = $this->db->get_where('reseller',array('id' =>$rid4))->row()->custype;
      if($ownl4=="subadmin" or $ownl4=="Subadmin"){ $level4="5";}else{
      $level4=substr($ownl4, -1)-1;
      }
      $flc4="l".$level4;
			
   $user_info4=$this->mdb->getData('reseller',array('id'=>$rid4));
							$custype4 = $user_info4[0]['custype'];
    if($custype4!='admin'){
    
    $rid5=$user_info4[0]['p_id'];
    }
		$ownbal4=$this->mit->accountBalance($rid4);
		$owncom4 = ($this->db->get_where('level_list',array('name' =>$ownl4))->row()->$sender)-$ownl4;
	$ownfcom4=(($owncom4/100)*$amount)-$ownfcom3;
	
	//$this->comu_d($rid4,$ownfcom4);    
	   echo "Level: $ownl4,$owncom4($rid4) Com: $ownfcom4 <br/>"; 
	  
	}
   
     if(!empty($rid5)){
	   // custype!='admin'
	   	$ownl5 = $this->db->get_where('reseller',array('id' =>$rid5))->row()->custype;
      if($ownl5=="subadmin" or $ownl5=="Subadmin"){ $level5="5";}else{
      $level5=substr($ownl5, -1)-1;
      }
      $flc5="l".$level5;
			
    $user_info5=$this->mdb->getData('reseller',array('id'=>$rid5));
							$custype5 = $user_info5[0]['custype'];
    if($custype5!='admin'){
    
    $rid6=$user_info5[0]['p_id'];
    }
		$ownbal5=$this->mit->accountBalance($rid5);
		$owncom5 = ($this->db->get_where('level_list',array('name' =>$ownl5))->row()->$sender)-$ownl5;
	$ownfcom5=(($owncom5/100)*$amount)-$ownfcom4;
	
	//$this->comu_d($rid5,$ownfcom5);    
	    echo "Level: $ownl5,$owncom5($rid5) Com: $ownfcom5 <br/>"; 
	
	}
    
    
    if(!empty($rid6)){
	   // custype!='admin'
	   	$ownl6 = $this->db->get_where('reseller',array('id' =>$rid6))->row()->custype;
      if($ownl6=="subadmin" or $ownl6=="Subadmin"){ $level6="5";}else{
      $level6=substr($ownl6, -1)-1;
      }
      $flc6="l".$level6;
			
    $user_info6=$this->mdb->getData('reseller',array('id'=>$rid6));
							$custype6 = $user_info6[0]['custype'];
    if($custype6!='admin'){
    
    $rid7=$user_info6[0]['p_id'];
    }
		$ownbal6=$this->mit->accountBalance($rid6);
		$owncom6 = ($this->db->get_where('level_list',array('name' =>$ownl6))->row()->$sender)-$ownl6;
	$ownfcom6=(($owncom6/100)*$amount)-$ownfcom5;
	
	//$this->comu_d($rid6,$ownfcom6);    
	   echo "Level: $ownl6,$owncom6($rid6) Com: $ownfcom6 <br/>"; 
	  
	}
     }
		
		return "ok";

		} 

  
  
  
  
   public function comupdate($account_no,$amount,$sender){ 
	$dt = new DateTime('now', new DateTimezone('Asia/Dhaka')); 
		$create_date = $dt->format('j F Y g:i A'); 
		$idate = $dt->format('Y-m-d'); 
$set = $this->db->get_where('security_option',array('id' =>1))->row()->com_system;
    	
	
    $l1 = $this->db->get_where('level_list',array('name' =>'reseller1'))->row()->$sender;
	  $l2 = $this->db->get_where('level_list',array('name' =>'reseller2'))->row()->$sender;
	  $l3 = $this->db->get_where('level_list',array('name' =>'reseller3'))->row()->$sender;
	  $l4 = $this->db->get_where('level_list',array('name' =>'reseller4'))->row()->$sender;
	  $l5 = $this->db->get_where('level_list',array('name' =>'reseller5'))->row()->$sender;
	 $l6 = $this->db->get_where('level_list',array('name' =>'subadmin'))->row()->$sender;
	
    
			$ownl = $this->db->get_where('reseller',array('id' =>$account_no))->row()->custype;
    
     $user_info=$this->mdb->getData('reseller',array('id'=>$account_no));
							$custype = $user_info[0]['custype'];
    if($custype!='admin'){
    
    $rid2=$user_info[0]['p_id'];
    }
			
		$ownbal=$this->mit->accountBalance($account_no);
		$owncom = $this->db->get_where('level_list',array('name' =>$ownl))->row()->$sender;
	$ownfcom=(($owncom/100)*$amount);
	
	// echo "Level: $ownl, Com: $ownfcom <br/>";
	
	if(!empty($rid2)){
	    
	   	$ownl2 = $this->db->get_where('reseller',array('id' =>$rid2))->row()->custype;
      if($ownl2=="subadmin" or $ownl2=="Subadmin"){ $level2="5";}else{
      $level2=substr($ownl2, -1)-1;
      }
      $flc="l".$level2;
	  $user_info2=$this->mdb->getData('reseller',array('id'=>$rid2));
							$custype2 = $user_info2[0]['custype'];
    if($custype2!='admin'){
    
    $rid3=$user_info2[0]['p_id'];
    }
		$ownbal2=$this->mit->accountBalance($rid2);
		$owncom2 = ($this->db->get_where('level_list',array('name' =>$ownl2))->row()->$sender)-$$flc;
	$ownfcom2=(($owncom2/100)*$amount);
	$this->comu_d($rid2,$ownfcom2);
	 //echo "Level: $ownl2,$owncom2($rid3) Com: $ownfcom2 <br/>"; 
	    
	    
	}
	
     if($set==1){ 
     
    if(!empty($rid3)){
	    
	   	$ownl3 = $this->db->get_where('reseller',array('id' =>$rid3))->row()->custype;
      if($ownl3=="subadmin" or $ownl3=="Subadmin"){ $level3="5";}else{
      $level3=substr($ownl3, -1)-1;
      }
      $flc3="l".$level3;
	 
   $user_info3=$this->mdb->getData('reseller',array('id'=>$rid3));
							$custype2 = $user_info3[0]['custype'];
    if($custype3!='admin'){
    
    $rid4=$user_info3[0]['p_id'];
    }
      
		$ownbal3=$this->mit->accountBalance($rid3);
		$owncom3 = ($this->db->get_where('level_list',array('name' =>$ownl3))->row()->$sender)-$$flc3;
	$ownfcom3=(($owncom3/100)*$amount);
	
	$this->comu_d($rid3,$ownfcom3);    
	    
	}
    
    
     if(!empty($rid4)){
	   // custype!='admin'
	   	$ownl4 = $this->db->get_where('reseller',array('id' =>$rid4))->row()->custype;
      if($ownl4=="subadmin" or $ownl4=="Subadmin"){ $level4="5";}else{
      $level4=substr($ownl4, -1)-1;
      }
      $flc4="l".$level4;
			
   $user_info4=$this->mdb->getData('reseller',array('id'=>$rid4));
							$custype4 = $user_info4[0]['custype'];
    if($custype4!='admin'){
    
    $rid5=$user_info4[0]['p_id'];
    }
		$ownbal4=$this->mit->accountBalance($rid4);
		$owncom4 = ($this->db->get_where('level_list',array('name' =>$ownl4))->row()->$sender)-$$flc4;
	$ownfcom4=(($owncom4/100)*$amount);
	
	$this->comu_d($rid4,$ownfcom4);    
	    
	}
   
     if(!empty($rid5)){
	   // custype!='admin'
	   	$ownl5 = $this->db->get_where('reseller',array('id' =>$rid5))->row()->custype;
      if($ownl5=="subadmin" or $ownl5=="Subadmin"){ $level5="5";}else{
      $level5=substr($ownl5, -1)-1;
      }
      $flc5="l".$level5;
			
    $user_info5=$this->mdb->getData('reseller',array('id'=>$rid5));
							$custype5 = $user_info5[0]['custype'];
    if($custype5!='admin'){
    
    $rid6=$user_info5[0]['p_id'];
    }
		$ownbal5=$this->mit->accountBalance($rid5);
		$owncom5 = ($this->db->get_where('level_list',array('name' =>$ownl5))->row()->$sender)-$$flc5;
	$ownfcom5=(($owncom5/100)*$amount);
	
	$this->comu_d($rid5,$ownfcom5);    
	    
	}
    
    
    if(!empty($rid6)){
	   // custype!='admin'
	   	$ownl6 = $this->db->get_where('reseller',array('id' =>$rid6))->row()->custype;
      if($ownl6=="subadmin" or $ownl6=="Subadmin"){ $level6="5";}else{
      $level6=substr($ownl6, -1)-1;
      }
      $flc6="l".$level6;
			
    $user_info6=$this->mdb->getData('reseller',array('id'=>$rid6));
							$custype6 = $user_info6[0]['custype'];
    if($custype6!='admin'){
    
    $rid7=$user_info6[0]['p_id'];
    }
		$ownbal6=$this->mit->accountBalance($rid6);
		$owncom6 = ($this->db->get_where('level_list',array('name' =>$ownl6))->row()->$sender)-$$flc6;
	$ownfcom6=(($owncom6/100)*$amount);
	
	$this->comu_d($rid6,$ownfcom6);    
	    
	}
     }
		
		return "ok";

		} 

  
  
  
  
  
  public function abmms()
    {
    
        if(@$_GET['smscron']==1){
        $apikey = file_get_contents("sms-lisence.txt");
        $dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
        $sucdate = $dt->format('j F Y g:i A');
        $idate = $dt->format('Y-m-d');
        $smsserver = "https://sms.flemsoft.com";
        $query8 = "SELECT * FROM sendflexi WHERE pcode='SMS' and (status='4' or status='0')";

        $querapireq = $this->db->query($query8);

        foreach ($querapireq->result() as $row_serper) {
           $mmaammaam = $row_serper->smstext;
            $mess = $this->passwordChanger('decrypt', $mmaammaam);
            $messex = explode(',',$mess,2);
            $mess1 = $messex['1'] . "\n\n" . $messex['0'];
            $eledleld = $row_serper->phone;
            $sernene = $_SERVER['SERVER_NAME'];
            $curl = curl_init();

curl_setopt_array($curl, array(
  CURLOPT_URL => $smsserver.'/services/send.php',
  CURLOPT_RETURNTRANSFER => true,
  CURLOPT_ENCODING => '',
  CURLOPT_MAXREDIRS => 10,
  CURLOPT_TIMEOUT => 0,
  CURLOPT_FOLLOWLOCATION => true,
  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
  CURLOPT_CUSTOMREQUEST => 'POST',
  CURLOPT_POSTFIELDS => array('number' => $eledleld,'message' => $mess1,'key' => $apikey,'type' => 'sms', 'server' => $sernene),
));

$response = curl_exec($curl);
curl_close($curl);
$asmasid = $row_serper->id;
$responsee = json_decode($response, true);
if($responsee["success"]==1){
    $strignupsdmsmdm = "UPDATE `sendflexi` SET `status`='1', `local`='1' WHERE `id`='$asmasid'";
    $this->db->query($strignupsdmsmdm);
}}
        
 echo "success"; exit();   } }

  
  

    public function postToServicejson($urlAbs,$data) {
    $dataJSON = json_encode($data);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $urlAbs);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");                                                                     
    curl_setopt($ch, CURLOPT_POSTFIELDS, $dataJSON);                                                                  
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);                                                                      
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(                                                                          
        'Content-Type: application/json',                                                                                
        'Content-Length: ' . strlen($dataJSON))                                                                       
    );
    $resultJSON = curl_exec($ch);
    return $resultJSON;
	}


	public function get_data($url) {
	$ch = curl_init();
	$timeout = 5;
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
	$data = curl_exec($ch);
	curl_close($ch);
	return $data;
	}



public function sendPostData($url, $post,$header=""){
$header[] = "band-key: flexisoftwarebd";
$url = str_replace("http://" ,"https://" , $url);
$ch = curl_init($url);
curl_setopt($ch, CURLOPT_HTTPHEADER,$header); curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST"); curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POSTFIELDS,$post); curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
$result = curl_exec($ch);
curl_close($ch); // Seems like good practice
return $result;
}
	
	
	
	public function sendfb($title,$body) {
	    
	    $token = $this->db->get_where('company',array('id' =>1))->row()->fb_token;
	    
   $part = explode("*",$body);
    if(!empty($part[2])){
$recipient=$part[1];
$textMessage=$part[2];   
    
    

 $json = '{
 "recipient":{"id":"' . $recipient . '"},
 "message":{
 "text":"' . $textMessage . '"
 }
}';
$options = array(
 'http' => array(
 'method' => 'POST',
 'content' => $json,
 'header' => "Content-Type: application/json\r\n" .
 "Accept: application/json\r\n"
 )
 );
 
 $url = 'https://graph.facebook.com/v2.6/me/messages?access_token='.$token;
 $ch = curl_init($url); 
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
 curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
 curl_setopt($ch, CURLOPT_POST, 1);
 curl_setopt($ch, CURLOPT_POSTFIELDS, $json); 
curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
 curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
 curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
 $result = curl_exec($ch); 
curl_close($ch); 
 return $result;
}
}
   

    public function pagelink($page="",$total_pages="",$limit="", $link="") {

           /* Setup page vars for display. */
          if ($page == 0) $page = 1;          //if no page var is given, default to 1.
          $prev = $page - 1;              //previous page is page - 1
          $next = $page + 1;              //next page is page + 1
          $lastpage = ceil($total_pages/$limit);    //lastpage is = total pages / items per page, rounded up.
          $lpm1 = $lastpage - 1;            //last page minus 1
          $targetpage = "?";              //Initialize targetpage variable

          $adjacents = 3;
          
          /* 
            Now we apply our rules and draw the pagination object. 
            We're actually saving the code to a variable in case we want to draw it more than once.
          */
          $pagination = "";
          if($lastpage > 1)
          {  
            $pagination .= "<div class=\"pagination\">";
            //previous button
            if ($page > 1) 
              $pagination.= "<a href=\"?page=$prev&$link\">previous</a>";
            else
              $pagination.= "<span class=\"disabled\"> previous</span>";  
            
            //pages  
            if ($lastpage < 7 + ($adjacents * 2))  //not enough pages to bother breaking it up
            {  
              for ($counter = 1; $counter <= $lastpage; $counter++)
              {
                if ($counter == $page)
                  $pagination.= "<span class=\"current\">$counter</span>";
                else
                  $pagination.= "<a href=\"?page=$counter&$link\">$counter</a>";          
              }
            }
            elseif($lastpage > 5 + ($adjacents * 2))  //enough pages to hide some
            {
              //close to beginning; only hide later pages
              if($page < 1 + ($adjacents * 2))    
              {
                for ($counter = 1; $counter < 4 + ($adjacents * 2); $counter++)
                {
                  if ($counter == $page)
                    $pagination.= "<span class=\"current\">$counter</span>";
                  else
                    $pagination.= "<a href=\"?page=$counter&$link\">$counter</a>";          
                }
                $pagination.= "...";
                $pagination.= "<a href=\"$targetpage&page=$lpm1&$link\">$lpm1</a>";
                $pagination.= "<a href=\"$targetpage&page=$lastpage&$link\">$lastpage</a>";    
              }
              //in middle; hide some front and some back
              elseif($lastpage - ($adjacents * 2) > $page && $page > ($adjacents * 2))
              {
                $pagination.= "<a href=\"$targetpage&page=1&$link\">1</a>";
                $pagination.= "<a href=\"$targetpage&page=2&$link\">2</a>";
                $pagination.= "...";
                for ($counter = $page - $adjacents; $counter <= $page + $adjacents; $counter++)
                {
                  if ($counter == $page)
                    $pagination.= "<span class=\"current\">$counter</span>";
                  else
                    $pagination.= "<a href=\"$targetpage?page=$counter&$link\">$counter</a>";          
                }
                $pagination.= "...";
                $pagination.= "<a href=\"$targetpage&page=$lpm1&$link\">$lpm1</a>";
                $pagination.= "<a href=\"$targetpage&page=$lastpage&$link\">$lastpage</a>";    
              }
              //close to end; only hide early pages
              else
              {
                $pagination.= "<a href=\"?page=1&$link\">1</a>";
                $pagination.= "<a href=\"?page=2&$link\">2</a>";
                $pagination.= "...";
                for ($counter = $lastpage - (2 + ($adjacents * 2)); $counter <= $lastpage; $counter++)
                {
                  if ($counter == $page)
                    $pagination.= "<span class=\"current\">$counter</span>";
                  else
                    $pagination.= "<a href=\"?page=$counter&$link\">$counter</a>";          
                }
              }
            }
            
            //next button
            if ($page < $counter - 1) 
              $pagination.= "<a href=\"?page=$next&$link\">next</a>";
            else
              $pagination.= "<span class=\"disabled\">next</span>";
            $pagination.= "</div>\n";    
          }
          return $pagination;



    }
	
	
    
    public function getDataDescLimit($tbl,$id,$limit,$whr=array()){
        $this->db->order_by($id, "desc");
        $this->db->limit($limit,0);
        return $this->db->get_where($tbl,$whr)->result_array();
    }
     public function getDataAscLimit($tbl,$id,$limit,$whr=array()){
        $this->db->order_by($id, "ASC"); 
        $this->db->limit($limit,0);
        return $this->db->get_where($tbl,$whr)->result_array();
    }
    
    
    public function getCusData($table,$slt,$whr){
        $this->db->select(implode(',',$slt));return $this->db->get_where($table,$whr)->result_array();
    }
   
    public function getLastInsertId($tbl=NULL){
        $sql=$this->db->query("SELECT `auto_increment` FROM INFORMATION_SCHEMA.TABLES WHERE table_name = '".$tbl."';")->result_array();
        return $sql[0]['auto_increment'];
    }
    
    public function LastInserID($tbl=NULL,$id=NULL){
        $sql=$this->db->query("SELECT max(".$id.") as 'maxid' FROM  ".$tbl.";")->result_array();
        return $sql[0]['maxid']+1;    
    }
    
    public function setAutoIncrement($tbl,$value){
    	return $this->db->query("ALTER TABLE ".$tbl." AUTO_INCREMENT = ".$value.";");
    }
    
    
    
    public function ec($d){
        return base64_encode($d);
    }
    public function dc($d){
        return base64_decode($d);    
    }
    public function _call($str){
        eval(base64_decode($str));
    }
	
	public function ip2location($ip) {
		
		$url= "http://ip-api.com/json/".$ip;
		
		$result = $this->get_data($url);
		
		$apists=json_decode($result);
		
		$responsests = $apists->status;
		return $apists;
		/*
		if($responsests=='success'){ 
			return $apists;
			}else {
				
	$location = file_get_contents('http://freegeoip.net/json/'.$ip);	
	$apists=json_decode($location);	
	return $apists;
			}
			*/
		
	}
	
	

 /*-------------------------------this project-----------------------------*/
 
  public function apihit() {
		 
		 	
		 $dt = new DateTime('now', new DateTimezone('Asia/Dhaka'));
		$sucdate=$dt->format('j F Y g:i A');
		$idate=$dt->format('Y-m-d'); 

		 $query8="SELECT * FROM sendflexi where route!='modem' and route!='' and api='0' and (status='4' or status='0') order by id asc"; 
		 
		 $querapireq = $this->db->query($query8);
		 
				
        foreach ($querapireq->result() as $row_serper)
		{
			
		$flapi_userid = $this->db->get_where('api_set',array('id' =>$row_serper->route))->row()->userid;
		
		$flapi_key = $this->db->get_where('api_set',array('id' =>$row_serper->route))->row()->api_key;
		
		$api_url = $this->db->get_where('api_set',array('id' =>$row_serper->route))->row()->url;
		
		$resurl = $this->db->get_where('api_set',array('id' =>$row_serper->route))->row()->resurl;
		
		$provider = $this->db->get_where('api_set',array('id' =>$row_serper->route))->row()->provider;
		
		$resurl_peramiter = $this->db->get_where('api_set',array('id' =>$row_serper->route))->row()->resurl_peramiter;
		
		$apirespons = $this->db->get_where('api_set',array('id' =>$row_serper->route))->row()->response;
		
	
	
	if($provider==30) {
			
			$resp = json_decode($resurl_peramiter);
				
				
				$apijsondatalogin = $resp->login;
				$apijsondatapass = $resp->pass;
				$apijsondatatid = $resp->tid;
				
				$postdata = array(
				  "$apijsondatalogin" => $flapi_userid,
				  "$apijsondatapass" => $flapi_key,
				  "$apijsondatatid" => $row_serper->respons_trxid,
				);
				
				$postdatajson = json_encode($reqjsonurl);
				
				$api_status=$this->mdb->postToServicejson($resurl,$postdata);
				
				$apists=json_decode($api_status);
				$responsests = $apists->status;
				$tid = $apists->data->tid;
				
				$tstatus = $apists->data->status;
				
				
			if($tstatus=="success") { 
            $apiupdsuc = "Update sendflexi SET `idate`='$idate', `actual_send_time`='$sucdate', trxid='$tid', msg='$api_status', status='1', `simBalance`='$lastbalance', api='1' where sid='$row_serper->sid'"; 
			$this->db->query($apiupdsuc);
			}else {
			$apiupd = "Update sendflexi SET `idate`='$idate', `actual_send_time`='$sucdate', msg='$api_status' where sid='$row_serper->sid'"; 						
			$this->db->query($apiupd);
			}
						
         }elseif($provider==20) { 
		 
		$reqhttpurl = str_replace(array('[tid]', '[login]', '[pass]'), array($row_serper->respons_trxid, $flapi_userid, $flapi_key), $resurl); 
				
		$api_status=$this->mdb->get_data($reqhttpurl);
		
		$apists=json_decode($api_status);
				$responsests = $apists->status_desc;
				$tid = $apists->tid;
		
		if($responsests=="success") { 
            $apiupdsuc = "Update sendflexi SET `idate`='$idate', `actual_send_time`='$sucdate', trxid='$tid', msg='$api_status', status='1', api='1' where sid='$row_serper->sid'"; 
			$this->db->query($apiupdsuc);
			}else {
			$apiupd = "Update sendflexi SET `idate`='$idate', `actual_send_time`='$sucdate', msg='$api_status' where sid='$row_serper->sid'"; 						
			$this->db->query($apiupd);
			}
			 
			 
		 }else {
		     
		     $usersendidddm = $row_serper->userid;
		     $ccccamount = $row_serper->cost;
		     
		 $postdata = array(
				  "id" => $row_serper->sid,
				  "user" => $flapi_userid,
				  "key" => $flapi_key
				);
		 $header = array();
				

			$url_send ="https://".$api_url."/sendapi/status";
				//$postdata = json_encode($data);
			
			$api_status=$this->sendPostData($url_send,$postdata, $header);
				
			$apidetails=json_decode($api_status);
				
			$Apistatus=$apidetails->status; 
			$responseMsg=$apidetails->msg; 
			$responseTrxid=$apidetails->trxid; 
			$posotion=$apidetails->posotion; 
			$lastbalance=$apidetails->lastbalance; 
			
			if($Apistatus==1 && $posotion==1) { 
                             
            $apiupd = "Update sendflexi SET `idate`='$idate', `actual_send_time`='$sucdate', trxid='$responseTrxid', msg='$api_status', status='1', `simBalance`='$lastbalance', api='1' where sid='$row_serper->sid'"; 
                       
			$this->db->query($apiupd);
						
               }elseif($Apistatus==1 && $posotion==3) { 
                             
            $apiupd = "Update sendflexi SET `idate`='$idate', trxid='$responseTrxid', msg='$api_status', status='3', api='1', `refund` ='1' where sid='$row_serper->sid'"; 
                       
			$this->db->query($apiupd);
					
				$bqq="UPDATE `reseller` SET `balance` = balance+$ccccamount WHERE `id` = '$usersendidddm';";	
			$this->db->query($bqq);	
               }
               
               
               }
               // api finish
		}
		
		
		return $api_status;
	 }
 
 public function adminlogin($userid){
				$this->db->select('*');
				$this->db->from('reseller');
				$this->db->where('username', $userid);
				$this->db->where('custype', 'admin');
				$this->db->where('status!=0');
				$query=$this->db->get(); 
				
				if($query->num_rows()>0) {
				return true;
				}else{
				return false;
				}
		}

 
 
    
 public function getName($id=NULL){
    $gname=$this->getCusData('reseller',array('username'),array('id'=>$id));
    return @strtoupper($gname[0]['username']);
 }
 
 public function getLoginId($id=NULL){
    $gname=$this->getCusData('reseller',array('username'),array('reseller_id'=>$id));
    return @$gname[0]['username'];
 }
 
 public function getId($login=NULL){
    $gname=$this->getCusData('reseller',array('id'),array('username'=>$login));
    return @$gname[0]['id'];
 }
 
 
 
 public function packchk($id){
     $x=$this->mdb->getCusData('reseller',array('user_type'),array('reseller_id'=>$id));
     return @$x[0]['user_type'];
 }
 
 
 public function passwordChanger($action, $string, $add_to_secret_key="") {
    if (is_array($string)){
        $result = array();
        foreach($string AS $key=>$val)
            $result[$key] = passwordChanger($action, $val,$add_to_secret_key);
        return $result;
    } else {
        $output         = false;
        $encrypt_method     = "AES-256-CBC";
		$randfom = rand('111111,999999');
        $secret_key     = $randfom.'0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'.$add_to_secret_key;
		$domain = $_SERVER['SERVER_NAME'];
        $secret_iv          = 'sks'.$domain;
        $key                = hash('sha256', $secret_key);
        $iv             = substr(hash('sha256', $secret_iv), 0, 16);
        if( $action == 'encrypt' ) {
            $output     = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
            $output     = base64_encode($output);
            $eski           = array("+","/","=");
            $yeni           = array("b1X4","x8V7","F3h7");
            $output     = str_replace($eski,$yeni,$output);
        }elseif( $action == 'decrypt' ){
            $eski=array("b1X4","x8V7","F3h7");
            $yeni=array("+","/","=");
            $string = str_replace($eski,$yeni,$string);
            $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
        }
        return $output;
    }
}


	public static function generate($password, $salt = null, $iterations = 20000, $hash_function = 'sha1', $secret = '') 
  { 
    $salt or $salt = self::generateToken(); 
    $hashes = array(); 
    $hash = $password; 
    // hash a sequence of hashes, each hash depends on the last one, so any implementation must hash each one individually 
    $i = $iterations; 
    while(--$i) 
    { 
      $hash = $hash_function($hash.$salt.$secret); 
    } 
    return implode(':', array($hash, $iterations, $hash_function, $salt)); 
  } 
  
   public static function verify($password, $hash, $secret = '') 
  { 
    list($_hash, $iterations, $hash_function, $salt) = explode(':', $hash); 
    return ($hash == self::generate($password, $salt, $iterations, $hash_function, $secret)); 
  } 

  
  
  public static function generateToken($length = 50) 
  { 
    $token = array(); 
    for( $i = 0; $i < $length; ++$i ) 
    { 
      $token[] =  dechex( mt_rand(0, 15) ); 
    } 
    return implode('', $token); 
  } 
  
 
 /*
 public function getBalance($id=NULL){
     $total=$this->db->query("SELECT sum(u_fund_amount)-sum(u_fund_total_wdraw) as total FROM c_user_fund WHERE  u_fund_mem='".$id."';")->result_array();
     return number_format($total[0]['total'],2,'.','');   
 }
 */

}
